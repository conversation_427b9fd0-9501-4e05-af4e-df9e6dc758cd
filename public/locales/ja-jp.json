{"Favorite": "お気に入り", "Tag": "タグ", "Duplicate": "重複", "Persona": "ペルソナ", "Delete": "削除", "AI Response Configuration": "AI応答の構成", "AI Configuration panel will stay open": "AI構成パネルが開いたままになります", "clickslidertips": "手動で値を入力するにはクリックしてください。", "MAD LAB MODE ON": "マッドラボモードオン", "Documentation on sampling parameters": "サンプリングパラメータのドキュメント", "kobldpresets": "Koboldのプリセット", "guikoboldaisettings": "KoboldAIのGUI設定", "Update current preset": "現在のプリセットを更新", "Save preset as": "プリセットを保存", "Import preset": "プリセットをインポート", "Export preset": "プリセットをエクスポート", "Restore current preset": "現在のプリセットを復元", "Delete the preset": "プリセットを削除", "novelaipresets": "NovelAIのプリセット", "Default": "デフォルト", "openaipresets": "OpenAIのプリセット", "Text Completion presets": "テキスト補完のプリセット", "AI Module": "AIモジュール", "Changes the style of the generated text.": "生成されたテキストのスタイルを変更します。", "No Module": "モジュールなし", "Instruct": "指導する", "Prose Augmenter": "散文増強装置", "Text Adventure": "テキストアドベンチャー", "response legth(tokens)": "応答の長さ（トークン数）", "Streaming": "ストリーミング", "Streaming_desc": "生成された応答を逐次表示します。", "context size(tokens)": "コンテキストのサイズ（トークン数）", "unlocked": "ロック解除", "Only enable this if your model supports context sizes greater than 8192 tokens": "モデルが8192トークンを超えるコンテキストサイズをサポートしている場合にのみ有効にします", "Max prompt cost:": "最大プロンプトコスト:", "Display the response bit by bit as it is generated.": "生成されるたびに、応答を逐次表示します。", "When this is off, responses will be displayed all at once when they are complete.": "この機能がオフの場合、応答は完全に生成されたときに一度ですべて表示されます。", "Temperature": "温度", "rep.pen": "繰り返しペナルティ", "Rep. Pen. Range.": "繰り返しペナルティの範囲", "Rep. Pen. Slope": "繰り返しペナルティスロープ", "Rep. Pen. Freq.": "繰り返しペナルティの頻度", "Rep. Pen. Presence": "繰り返しペナルティの存在", "TFS": "TFS", "Phrase Repetition Penalty": "フレーズの繰り返しペナルティ", "Off": "オフ", "Very light": "非常に軽い", "Light": "軽め", "Medium": "中程度", "Aggressive": "強め", "Very aggressive": "非常に強い", "Unlocked Context Size": "ロック解除されたコンテキストサイズ", "Unrestricted maximum value for the context slider": "コンテキストスライダーの制限なしの最大値", "Context Size (tokens)": "コンテキストサイズ（トークン数）", "Max Response Length (tokens)": "最大応答長（トークン数）", "Multiple swipes per generation": "世代ごとに複数のスワイプ", "Enable OpenAI completion streaming": "OpenAIの完了ストリーミングを有効にする", "Frequency Penalty": "頻度ペナルティ", "Presence Penalty": "存在ペナルティ", "Count Penalty": "カウントペナルティ", "Top K": "トップK", "Top P": "トップP", "Repetition Penalty": "繰り返しペナルティ", "Min P": "最小P", "Top A": "トップA", "Quick Prompts Edit": "クイックプロンプトの編集", "Main": "メイン", "NSFW": "閲覧注意", "Jailbreak": "脱獄", "Utility Prompts": "ユーティリティプロンプト", "Impersonation prompt": "なりすましプロンプト", "Restore default prompt": "デフォルトのプロンプトを復元", "Prompt that is used for Impersonation function": "なりすまし機能に使用されるプロンプト", "World Info Format Template": "世界情報フォーマットテンプレート", "Restore default format": "デフォルトの形式を復元する", "Wraps activated World Info entries before inserting into the prompt.": "プロンプトに挿入する前に、アクティブ化された World Info エントリをラップします。", "scenario_format_template_part_1": "使用", "scenario_format_template_part_2": "コンテンツが挿入される場所をマークします。", "Scenario Format Template": "シナリオ形式テンプレート", "Personality Format Template": "パーソナリティフォーマットテンプレート", "Group Nudge Prompt Template": "グループナッジプロンプトテンプレート", "Sent at the end of the group chat history to force reply from a specific character.": "グループチャット履歴の最後に送信して、特定のキャラクターからの返信を強制します。", "New Chat": "新しいチャット", "Restore new chat prompt": "新しいチャットプロンプトを復元する", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "チャット履歴の先頭に設定して、新しいチャットが開始されることを示します。", "New Group Chat": "新しいグループチャット", "Restore new group chat prompt": "デフォルトのプロンプトを復元する", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "チャット履歴の先頭に設定して、新しいグループチャットが開始されることを示します。", "New Example Chat": "新しいサンプルチャット", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "ダイアログ例の先頭に設定して、新しいサンプル チャットが開始されることを示します。", "Continue nudge": "ナッジを続ける", "Set at the end of the chat history when the continue button is pressed.": "続行ボタンを押したときにチャット履歴の最後に設定します。", "Replace empty message": "空のメッセージを置換", "Send this text instead of nothing when the text box is empty.": "テキストボックスが空の場合、何もない代わりにこのテキストを送信します。", "Seed": "シード", "Set to get deterministic results. Use -1 for random seed.": "決定論的な結果を得るために設定します。ランダム シードには -1 を使用します。", "Temperature controls the randomness in token selection": "温度はトークン選択のランダム性を制御します", "Top_K_desc": "Top Kは選択できるトップトークンの最大量を設定します", "Top_P_desc": "Top P（別名核サンプリング）", "Typical P": "典型的なP", "Typical_P_desc": "典型的なPサンプリングは、セットの平均エントロピーからの偏差に基づいてトークンを優先します", "Min_P_desc": "Min Pは基本的な最小確率を設定します", "Top_A_desc": "Top Aは最高のトークン確率の二乗に基づいてトークン選択の閾値を設定します", "Tail_Free_Sampling_desc": "Tail-Freeサンプリング（TFS）", "rep.pen range": "繰り返しペナルティの範囲", "Mirostat": "ミロスタット", "Mode": "モード", "Mirostat_Mode_desc": "値 0 は Mirostat を完全に無効にします。1 は Mirostat 1.0、2 は Mirostat 2.0 です。", "Tau": "タウ", "Mirostat_Tau_desc": "ミロスタット出力の変動を制御", "Eta": "エタ", "Mirostat_Eta_desc": "Mirostatの学習率を制御する", "Ban EOS Token": "EOSトークンを禁止", "Ban_EOS_Token_desc": "KoboldCpp の End-of-Sequence (EOS) トークンを禁止します (KoboldAI の他のトークンも禁止される可能性があります)。ストーリー作成には適していますが、チャットや指示モードでは使用しないでください。", "GBNF Grammar": "GBNF文法", "Type in the desired custom grammar": "必要なカスタム文法を入力してください", "Samplers Order": "サンプラーの順序", "Samplers will be applied in a top-down order. Use with caution.": "サンプラーは上から下への順序で適用されます。注意して使用してください。", "Tail Free Sampling": "テールフリーサンプリング", "Load koboldcpp order": "koboldcppオーダーを読み込む", "Preamble": "前文", "Use style tags to modify the writing style of the output.": "スタイルタグを使用して、出力の書き方を変更します。", "Banned Tokens": "禁止されたトークン", "Sequences you don't want to appear in the output. One per line.": "出力に表示したくないシーケンス。1行に1つ。", "Logit Bias": "ログのバイアス", "Add": "追加", "Helps to ban or reenforce the usage of certain words": "特定の単語の使用を禁止または強化するのに役立ちます", "CFG Scale": "CFGスケール", "Negative Prompt": "ネガティブプロンプト", "Add text here that would make the AI generate things you don't want in your outputs.": "出力に望ましくないものを生成させるAIを作成するテキストをここに追加します。", "Used if CFG Scale is unset globally, per chat or character": "CFGスケールがグローバル、チャットごと、またはキャラクターごとに設定されていない場合に使用されます", "Mirostat Tau": "Mirostat Tau", "Mirostat LR": "ミロスタットLR", "Min Length": "最小長", "Top K Sampling": "トップKサンプリング", "Nucleus Sampling": "核サンプリング", "Top A Sampling": "トップAサンプリング", "CFG": "CFG", "Neutralize Samplers": "サンプラーを中立化する", "Set all samplers to their neutral/disabled state.": "すべてのサンプラーを中立/無効の状態に設定します。", "Sampler Select": "サンプラー選択", "Customize displayed samplers or add custom samplers.": "表示されるサンプラーをカスタマイズするか、カスタム サンプラーを追加します。", "Epsilon Cutoff": "イプシロンカットオフ", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "イプシロンカットオフは、サンプリング対象から除外されるトークンの下限確率を設定します", "Eta Cutoff": "エタカットオフ", "Eta_Cutoff_desc": "エータカットオフは、特別なエータサンプリング技術の主要なパラメータです。&#13;1e-4の単位で; 合理的な値は3です。&#13;無効にするには0に設定します。&#13;詳細については、Hewittらによる論文「言語モデルデスムージングの切断サンプリング」（2022）を参照してください。", "rep.pen decay": "ペンの劣化", "Encoder Rep. Pen.": "エンコーダー繰り返しペナルティ", "No Repeat Ngram Size": "繰り返しのないNgramサイズ", "Skew": "斜め", "Max Tokens Second": "最大トークン/秒", "Smooth Sampling": "スムーズサンプリング", "Smooth_Sampling_desc": "2 次/3 次変換を使用して分布を調整できます。スムージング係数の値を低くすると、よりクリエイティブになります。通常、0.2 ～ 0.3 がスイートスポットです (曲線 = 1 と仮定)。スムージング曲線の値が高くなると、曲線が急勾配になり、確率の低い選択がより積極的に罰せられます。1.0 曲線は、スムージング係数のみを使用するのと同じです。", "Smoothing Factor": "平滑化係数", "Smoothing Curve": "スムージングカーブ", "DRY_Repetition_Penalty_desc": "DRY は、入力の末尾を、入力内で以前に発生したシーケンスに拡張するトークンにペナルティを課します。無効にするには、乗数を 0 に設定します。", "DRY Repetition Penalty": "DRY繰り返しペナルティ", "DRY_Multiplier_desc": "DRY を有効にするには、値を 0 より大きく設定します。ペナルティが課される最短シーケンスのペナルティの大きさを制御します。", "Multiplier": "乗数", "DRY_Base_desc": "シーケンスの長さの増加に伴ってペナルティが増加する速度を制御します。", "Base": "ベース", "DRY_Allowed_Length_desc": "ペナルティなしで繰り返すことができる最長のシーケンス。", "Allowed Length": "許容長さ", "Penalty Range": "ペナルティ範囲", "DRY_Sequence_Breakers_desc": "シーケンスのマッチングが継続されないトークン。引用符で囲まれた文字列のコンマ区切りリストとして指定します。", "Sequence Breakers": "シーケンスブレーカー", "JSON-serialized array of strings.": "JSON シリアル化された文字列の配列。", "Dynamic Temperature": "動的温度", "Scale Temperature dynamically per token, based on the variation of probabilities": "確率の変動に基づいて、トークンごとに温度を動的にスケーリングします", "Minimum Temp": "最低温度", "Maximum Temp": "最大温度", "Exponent": "指数", "Mirostat (mode=1 is only for llama.cpp)": "ミロスタット（mode=1はllama.cpp用）", "Mirostat_desc": "ミロスタットは出力の混乱度のためのサーモスタットです", "Mirostat Mode": "Mirostatモード", "Variability parameter for Mirostat outputs": "Mirostat出力の変動パラメータ", "Mirostat Eta": "Mirostat Eta", "Learning rate of Mirostat": "Mirostatの学習率", "Beam search": "ビームサーチ", "Helpful tip coming soon.": "役立つヒントがすぐに公開されます。", "Number of Beams": "ビームの数", "Length Penalty": "長さペナルティ", "Early Stopping": "早期停止", "Contrastive search": "対照的な検索", "Penalty Alpha": "ペナルティアルファ", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "コントラスティブサーチの正則化項の強度。CSを無効にするには0に設定します", "Do Sample": "サンプルを行う", "Add BOS Token": "BOSトークンを追加", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "プロンプトの先頭にbos_tokenを追加します。これを無効にすると、返信がよりクリエイティブになります", "Ban the eos_token. This forces the model to never end the generation prematurely": "eos_tokenを禁止します。これにより、モデルが生成を早期に終了することがなくなります", "Ignore EOS Token": "EOSトークンを無視", "Ignore the EOS Token even if it generates.": "EOS トークンが生成されても無視します。", "Skip Special Tokens": "特別なトークンをスキップ", "Temperature Last": "最後の温度", "Temperature_Last_desc": "最後に温度サンプラを使用します", "Speculative Ngram": "推測的Nグラム", "Use a different speculative decoding method without a draft model": "ドラフト モデルなしで、別の推測デコード方法を使用します。ドラフト モデルの使用が推奨されます。推測 ngram はそれほど効果的ではありません。", "Spaces Between Special Tokens": "特殊トークン間のスペース", "LLaMA / Mistral / Yi models only": "LLaMA / Mistral / Yiモデルのみ", "Example: some text [42, 69, 1337]": "例：いくつかのテキスト[42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "分類器フリーガイダンス。より役立つヒントが近日公開されます", "Scale": "スケール", "JSON Schema": "JSONスキーマ", "Type in the desired JSON schema": "希望するJSONスキーマを入力します", "Grammar String": "文法文字列", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF または EBNF は、使用するバックエンドによって異なります。これを使用する場合は、どちらであるかを知っておく必要があります。", "Top P & Min P": "トップPと最小P", "Load default order": "デフォルトの順序を読み込む", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "llama.cpp のみ。サンプラーの順序を決定します。Mirostat モードが 0 でない場合、サンプラーの順序は無視されます。", "Sampler Priority": "サンプラー優先度", "Ooba only. Determines the order of samplers.": "<PERSON><PERSON><PERSON>のみ。サンプラーの順序を決定します。", "Character Names Behavior": "キャラクター名の動作", "Helps the model to associate messages with characters.": "モデルがメッセージをキャラクターに関連付けるのに役立ちます。", "None": "なし", "character_names_default": "グループと過去のペルソナを除きます。それ以外の場合は、プロンプトに名前を必ず入力してください。", "Don't add character names.": "キャラクター名を追加しないでください。", "Completion": "完了オブジェクト", "character_names_completion": "制限事項: ラテン英数字とアンダースコアのみ。すべてのソースで機能するわけではありません。特に、Claude、MistralAI、Google では機能しません。", "Add character names to completion objects.": "完了オブジェクトにキャラクター名を追加します。", "Message Content": "メッセージ内容", "Prepend character names to message contents.": "メッセージの内容の先頭にキャラクター名を追加します。", "Continue Postfix": "ポストフィックスの継続", "The next chunk of the continued message will be appended using this as a separator.": "継続メッセージの次のチャンクは、これを区切り文字として使用して追加されます。", "Space": "空間", "Newline": "改行", "Double Newline": "二重改行", "Wrap user messages in quotes before sending": "送信前にユーザーメッセージを引用符で囲む", "Wrap in Quotes": "引用符で囲む", "Wrap entire user message in quotes before sending.": "送信前にユーザーメッセージ全体を引用符で囲みます。", "Leave off if you use quotes manually for speech.": "発言のために引用符を手動で使用する場合はオフにします。", "Continue prefill": "プリフィルの継続", "Continue sends the last message as assistant role instead of system message with instruction.": "続行は、最後のメッセージをシステムメッセージとしてではなく、アシスタントの役割として送信します。", "Squash system messages": "システムメッセージを結合する", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "連続するシステムメッセージを1つに結合します（例のダイアログを除く）。一部のモデルの一貫性を向上させる可能性があります。", "Enable function calling": "関数呼び出しを有効にする", "Send inline images": "インライン画像を送信", "image_inlining_hint_1": "モデルがサポートしている場合、プロンプトで画像を送信します。", "image_inlining_hint_2": "メッセージに対するアクションまたは", "image_inlining_hint_3": "チャットに画像ファイルを添付するためのメニュー。", "Inline Image Quality": "インライン画像品質", "openai_inline_image_quality_auto": "オート", "openai_inline_image_quality_low": "低い", "openai_inline_image_quality_high": "高い", "Use AI21 Tokenizer": "AI21トークナイザーを使用する", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "GPT よりも効率的な、ジュラシック モデルに適したトークナイザーを使用します。", "Use Google Tokenizer": "Googleトークナイザーを使用", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Googleモデル用の適切なトークナイザーを使用します。 API経由で。 処理が遅くなりますが、トークンの数え上げがはるかに正確になります。", "Use system prompt": "システムプロンプトを使用する", "(Gemini 1.5 Pro/Flash only)": "(Gemini 1.5 Pro/Flash のみ)", "Merges_all_system_messages_desc_1": "非システムロールの最初のメッセージまでのすべてのシステムメッセージをマージし、それらを", "Merges_all_system_messages_desc_2": "分野。", "Assistant Prefill": "アシスタントプリフィル", "Start Claude's answer with...": "クロードの回答を...で始める", "Assistant Impersonation Prefill": "アシスタントのなりすまし事前入力", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "サポートされているモデルのシステムプロンプトを送信します。無効にすると、ユーザーメッセージがプロンプトの先頭に追加されます。", "User first message": "ユーザーの最初のメッセージ", "Restore User first message": "ユーザーの最初のメッセージを復元する", "Human message": "人間によるメッセージ、指示など。\n空の場合は何も追加されません。つまり、ロール「ユーザー」を持つ新しいプロンプトが必要です。", "New preset": "新しいプリセット", "Delete preset": "プリセットを削除", "View / Edit bias preset": "バイアスプリセットを表示/編集", "Add bias entry": "バイアスエントリを追加", "Most tokens have a leading space.": "ほとんどのトークンには先頭にスペースがあります。", "API Connections": "API接続", "Text Completion": "テキスト補完", "Chat Completion": "チャット完了", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "Hordeに機密情報を送信しないでください。", "Review the Privacy statement": "プライバシー声明を確認する", "Register a Horde account for faster queue times": "キュー待ち時間を短縮するためにHordeアカウントを登録する", "Learn how to contribute your idle GPU cycles to the Horde": "アイドルのGPUサイクルをホルドに貢献する方法を学びます", "Adjust context size to worker capabilities": "ワーカーの能力に応じてコンテキストサイズを調整する", "Adjust response length to worker capabilities": "ワーカーの能力に応じて応答の長さを調整する", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "承認済みの作業者のみをキューに入れることで、悪い応答に対処できます。応答時間が遅くなる場合があります。", "Trusted workers only": "信頼されたワーカーのみ", "API key": "APIキー", "Get it here:": "こちらから入手:", "Register": "登録", "View my Kudos": "私の称賛を表示", "Enter": "入力", "to use anonymous mode.": "匿名モードを使用するには。", "Clear your API key": "APIキーをクリア", "For privacy reasons, your API key will be hidden after you reload the page.": "プライバシーの理由から、ページを再読み込みするとAPIキーが非表示になります。", "Models": "モデル", "Refresh models": "モデルをリフレッシュ", "-- Horde models not loaded --": "-- ホルドモデルがロードされていません --", "Not connected...": "接続されていません...", "API url": "API URL", "Example: http://127.0.0.1:5000/api ": "例：http://127.0.0.1:5000/api", "Connect": "接続", "Cancel": "キャンセル", "Novel API key": "NovelAIのAPIキー", "Get your NovelAI API Key": "NovelAI APIキーを取得する", "Enter it in the box below": "以下のボックスに入力してください", "Novel AI Model": "NovelAIモデル", "No connection...": "接続なし...", "API Type": "APIタイプ", "Default (completions compatible)": "デフォルト [OpenAI /補完互換: oobabooga、LM Studio など]", "TogetherAI API Key": "TogetherAIのAPIキー", "TogetherAI Model": "TogetherAIモデル", "-- Connect to the API --": "-- APIに接続 --", "OpenRouter API Key": "OpenRouterのAPIキー", "Click Authorize below or get the key from": "以下の[承認]をクリックするか、キーを取得する", "View Remaining Credits": "残高を表示", "OpenRouter Model": "OpenRouterモデル", "Model Providers": "モデルプロバイダー", "InfermaticAI API Key": "InfermaticAI API キー", "InfermaticAI Model": "InfermaticAI モデル", "DreamGen API key": "DreamGen APIキー", "DreamGen Model": "ドリームジェンモデル", "Mancer API key": "MancerのAPIキー", "Mancer Model": "マンサーモデル", "Make sure you run it with": "次のように実行していることを確認してください", "flag": "フラグ", "API key (optional)": "APIキー（オプション）", "Server url": "サーバーURL", "Example: http://127.0.0.1:5000": "例: http://127.0.0.1:5000", "Custom model (optional)": "カスタムモデル（オプション）", "vllm-project/vllm": "vllm-project/vllm (OpenAI API ラッパーモード)", "vLLM API key": "vLLM API キー", "Example: http://127.0.0.1:8000": "例: http://127.0.0.1:8000", "vLLM Model": "vLLM モデル", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine（OpenAI APIエンドポイントのパッケージングモード）", "Aphrodite API key": "アフロディーテAPIキー", "Aphrodite Model": "アフロディーテモデル", "ggerganov/llama.cpp": "ggerganov/llama.cpp（出力サーバー）", "Example: http://127.0.0.1:8080": "例: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "例: http://127.0.0.1:11434", "Ollama Model": "Ollamaモデル", "Download": "ダウンロード", "Tabby API key": "<PERSON><PERSON>", "koboldcpp API key (optional)": "koboldcpp API キー (オプション)", "Example: http://127.0.0.1:5001": "例: http://127.0.0.1:5001", "Authorize": "承認", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "OAuthフローを使用してOpenRouter APIトークンを取得します。 openrouter.aiにリダイレクトされます", "Bypass status check": "ステータスのチェックをバイパスする", "Chat Completion Source": "チャット補完ソース", "Reverse Proxy": "リバースプロキシ", "Proxy Presets": "プロキシプリセット", "Saved addresses and passwords.": "保存されたアドレスとパスワード。", "Save Proxy": "プロキシを保存", "Delete Proxy": "プロキシを削除", "Proxy Name": "プロキシ名", "This will show up as your saved preset.": "これは保存したプリセットとして表示されます。", "Proxy Server URL": "プロキシサーバー URL", "Alternative server URL (leave empty to use the default value).": "代替サーバーのURL（デフォルト値を使用するには空白のままにしてください）。", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "このボックスに何か入力する前に、APIパネルから実際のOAI APIキーを削除してください", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "非公式のOpenAIプロキシを使用して問題が発生した場合、サポートを提供することができません", "Doesn't work? Try adding": "うまくいかない場合は追加してみてください", "at the end!": "最後に！", "Proxy Password": "プロキシパスワード", "Will be used as a password for the proxy instead of API key.": "API キーの代わりにプロキシのパスワードとして使用されます。", "Peek a password": "パスワードを覗く", "OpenAI API key": "OpenAI APIキー", "View API Usage Metrics": "API使用メトリクスを表示", "Follow": "フォロー", "these directions": "これらの指示", "to get your OpenAI API key.": "OpenAIのAPIキーを取得するために。", "Use Proxy password field instead. This input will be ignored.": "代わりに「プロキシ パスワード」フィールドを使用してください。この入力は無視されます。", "OpenAI Model": "OpenAIモデル", "Bypass API status check": "APIステータスのチェックをバイパスする", "Show External models (provided by API)": "外部モデルを表示（APIで提供される）", "Get your key from": "キーを取得する", "Anthropic's developer console": "Anthropicの開発者コンソール", "Claude Model": "Claudeモデル", "Window AI Model": "Window AIモデル", "Model Order": "OpenRouter モデルのソート", "Alphabetically": "アルファベット順", "Price": "価格（最安値）", "Context Size": "コンテキストサイズ", "Group by vendors": "ベンダー別にグループ化", "Group by vendors Description": "OpenAI モデルを 1 つのグループに、Anthropic モデルを別のグループに配置するなどします。ソートと組み合わせることができます。", "Allow fallback routes": "フォールバックルートを許可", "Allow fallback routes Description": "選択したモデルが要求を満たせない場合、代替モデルが自動的に選択されます。", "Scale API Key": "ScaleのAPIキー", "Clear your cookie": "クッキーを消去する", "Alt Method": "代替手法", "AI21 API Key": "AI21のAPIキー", "AI21 Model": "AI21モデル", "Google AI Studio API Key": "Google AI Studio APIキー", "Google Model": "Google モデル", "MistralAI API Key": "MistralAI API キー", "MistralAI Model": "MistralAI モデル", "Groq API Key": "Groq API キー", "Groq Model": "Groq モデル", "Perplexity API Key": "パープレキシティ API キー", "Perplexity Model": "困惑モデル", "Cohere API Key": "Cohere API キー", "Cohere Model": "Cohereモデル", "Custom Endpoint (Base URL)": "カスタムエンドポイント (ベース URL)", "Custom API Key": "カスタム API キー", "Available Models": "利用可能なモデル", "Prompt Post-Processing": "迅速な後処理", "Applies additional processing to the prompt before sending it to the API.": "プロンプトを API に送信する前に追加の処理を適用します。", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "短いテストメッセージを送信してAPI接続を検証します。それに対してクレジットが与えられることに注意してください！", "Test Message": "テストメッセージ", "Auto-connect to Last Server": "前回のサーバーに自動接続", "Missing key": "❌ キーがありません", "Key saved": "✔️ キーを保存しました", "View hidden API keys": "隠しAPIキーを表示", "AI Response Formatting": "AI応答の書式設定", "Advanced Formatting": "高度なフォーマット", "Context Template": "コンテキストテンプレート", "Auto-select this preset for Instruct Mode": "Instructモードのためにこのプリセットを自動選択", "Story String": "ストーリー文字列", "Example Separator": "例のセパレータ", "Chat Start": "チャット開始", "Add Chat Start and Example Separator to a list of stopping strings.": "停止文字列のリストにチャット開始と例の区切り文字を追加します。", "Use as Stop Strings": "ストップ文字列として使用", "Allow Jailbreak": "脱獄を許可する", "Context Order": "コンテキスト順序", "Summary": "まとめ", "Author's Note": "著者の注意", "Example Dialogues": "対話例", "Hint": "ヒント：", "In-Chat Position not affected": "概要と著者のメモの注文は、チャット内の位置が設定されていない場合にのみ影響を受けます。", "Instruct Mode": "指示モード", "Enabled": "有効", "instruct_bind_to_context": "有効にすると、選択した指示テンプレート名または設定に基づいてコンテキスト テンプレートが自動的に選択されます。", "Bind to Context": "コンテキストにバインド", "Presets": "プリセット", "Auto-select this preset on API connection": "API接続時にこのプリセットを自動選択", "Activation Regex": "アクティベーション正規表現", "Wrap Sequences with Newline": "シーケンスを改行でラップする", "Replace Macro in Sequences": "シーケンス内のマクロを置換", "Skip Example Dialogues Formatting": "例の対話の書式設定をスキップ", "Include Names": "名前を含める", "Force for Groups and Personas": "グループと人物用の強制", "System Prompt": "システムプロンプト", "Instruct Mode Sequences": "指示モードシーケンス", "System Prompt Wrapping": "システムプロンプトの折り返し", "Inserted before a System prompt.": "システムプロンプトの前に挿入されます。", "System Prompt Prefix": "システムプロンプトプレフィックス", "Inserted after a System prompt.": "システムプロンプトの後に挿入されます。", "System Prompt Suffix": "システムプロンプトサフィックス", "Chat Messages Wrapping": "チャットメッセージの折り返し", "Inserted before a User message and as a last prompt line when impersonating.": "ユーザー メッセージの前に挿入され、偽装時の最後のプロンプト行として挿入されます。", "User Message Prefix": "ユーザーメッセージプレフィックス", "Inserted after a User message.": "ユーザー メッセージの後に挿入されます。", "User Message Suffix": "ユーザーメッセージサフィックス", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "アシスタント メッセージの前に挿入され、AI 応答を生成するときの最後のプロンプト ラインとして挿入されます。", "Assistant Message Prefix": "アシスタントメッセージプレフィックス", "Inserted after an Assistant message.": "アシスタントメッセージの後に挿入されます。", "Assistant Message Suffix": "アシスタントメッセージサフィックス", "Inserted before a System (added by slash commands or extensions) message.": "システム (スラッシュ コマンドまたは拡張機能によって追加された) メッセージの前に挿入されます。", "System Message Prefix": "システムメッセージプレフィックス", "Inserted after a System message.": "システム メッセージの後に挿入されます。", "System Message Suffix": "システムメッセージサフィックス", "If enabled, System Sequences will be the same as User Sequences.": "有効にすると、システム シーケンスはユーザー シーケンスと同じになります。", "System same as User": "システムとユーザーは同じ", "Misc. Sequences": "その他のシーケンス", "Inserted before the first Assistant's message.": "最初のアシスタントのメッセージの前に挿入されます。", "First Assistant Prefix": "第一アシスタントプレフィックス", "instruct_last_output_sequence": "最後のアシスタントのメッセージの前に挿入されるか、AI 応答を生成するときの最後のプロンプト ラインとして挿入されます (ニュートラル/システム ロールを除く)。", "Last Assistant Prefix": "最後のアシスタントプレフィックス", "Will be inserted as a last prompt line when using system/neutral generation.": "システム/ニュートラル生成を使用する場合、最後のプロンプト行として挿入されます。", "System Instruction Prefix": "システム命令プレフィックス", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "停止シーケンスが生成されると、それ以降のすべてが出力から削除されます (含む)。", "Stop Sequence": "停止シーケンス", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "チャット履歴がユーザー メッセージで始まっていない場合は、チャット履歴の先頭に挿入されます。", "User Filler Message": "ユーザーフィラーメッセージ", "Context Formatting": "コンテキストのフォーマット", "(Saved to Context Template)": "(コンテキストテンプレートに保存)", "Always add character's name to prompt": "プロンプトにキャラクターの名前を常に追加する", "Generate only one line per request": "リクエストごとに1行のみ生成", "Trim Incomplete Sentences": "不完全な文をトリミング", "Include Newline": "改行を含む", "Misc. Settings": "その他の設定", "Collapse Consecutive Newlines": "連続する改行を折りたたむ", "Trim spaces": "スペースをトリミング", "Tokenizer": "トークナイザー", "Token Padding": "トークンのパディング", "Start Reply With": "返信の開始", "AI reply prefix": "AI応答のプレフィックス", "Show reply prefix in chat": "チャットで返信の接頭辞を表示", "Non-markdown strings": "マークダウン以外の文字列", "separate with commas w/o space between": "間にスペースのないカンマで区切ります", "Custom Stopping Strings": "カスタム停止文字列", "JSON serialized array of strings": "文字列のJSONシリアル化配列", "Replace Macro in Stop Strings": "カスタム停止文字列内のマクロを置換する", "Auto-Continue": "自動継続", "Allow for Chat Completion APIs": "チャット補完APIを許可", "Target length (tokens)": "ターゲット長さ（トークン）", "World Info": "世界情報", "Locked = World Editor will stay open": "ロックされた = ワールドエディターが開いたままになります", "Worlds/Lorebooks": "ワールド/Lorebook", "Active World(s) for all chats": "すべてのチャットのアクティブなワールド", "-- World Info not found --": "-- ワールド情報が見つかりません --", "Global World Info/Lorebook activation settings": "グローバルワールド情報/Lorebookの有効化設定", "Click to expand": "クリックして展開", "Scan Depth": "スキャンの深さ", "Context %": "コンテキスト％", "Budget Cap": "予算キャップ", "(0 = disabled)": "(0 = 無効)", "Scan chronologically until reached min entries or token budget.": "最小エントリ数またはトークン予算に達するまで時系列でスキャンします。", "Min Activations": "最小アクティベーション", "Max Depth": "最大深度", "(0 = unlimited, use budget)": "(0 = 無制限、予算を使用)", "Insertion Strategy": "挿入戦略", "Sorted Evenly": "均等に並べ替え", "Character Lore First": "キャラクターロアを最初に表示", "Global Lore First": "グローバルロアを最初に表示", "Entries can activate other entries by mentioning their keywords": "エントリは、キーワードを言及することで他のエントリをアクティブにできます", "Recursive Scan": "再帰的スキャン", "Lookup for the entry keys in the context will respect the case": "コンテキスト内のエントリキーの検索は、ケースを尊重します", "Case Sensitive": "大文字と小文字を区別する", "If the entry key consists of only one word, it would not be matched as part of other words": "エントリキーが1つの単語だけで構成されている場合、他の単語の一部として一致しません", "Match Whole Words": "完全一致", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "包含グループのフィルタリングでは、キー一致数が最も多いエントリのみが選択されます。", "Use Group Scoring": "グループスコアリングを使用する", "Alert if your world info is greater than the allocated budget.": "ワールド情報が割り当てられた予算より大きい場合に警告します。", "Alert On Overflow": "オーバーフロー時に警告", "New": "新規", "or": "または", "--- Pick to Edit ---": "--- 編集するものを選択 ---", "Rename World Info": "ワールド情報の名前を変更", "Open all Entries": "すべてのエントリを開く", "Close all Entries": "すべてのエントリを閉じる", "New Entry": "新しいエントリ", "Fill empty Memo/Titles with Keywords": "空のメモ/タイトルにキーワードを入れる", "Import World Info": "ワールド情報のインポート", "Export World Info": "ワールド情報のエクスポート", "Duplicate World Info": "ワールド情報の複製", "Delete World Info": "ワールド情報を削除", "Search...": "検索...", "Search": "検索", "Priority": "優先度", "Custom": "カスタム", "Title A-Z": "タイトルA-Z", "Title Z-A": "タイトルZ-A", "Tokens ↗": "トークン ↗", "Tokens ↘": "トークン ↘", "Depth ↗": "深さ ↗", "Depth ↘": "深さ ↘", "Order ↗": "順序 ↗", "Order ↘": "順序 ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "トリガー% ↗", "Trigger% ↘": "トリガー% ↘", "Refresh": "更新", "User Settings": "ユーザー設定", "Simple": "シンプル", "Advanced": "高度", "UI Language": "言語", "Account": "アカウント", "Admin Panel": "管理パネル", "Logout": "ログアウト", "Search Settings": "検索設定", "UI Theme": "UIテーマ", "Import a theme file": "テーマファイルをインポート", "Export a theme file": "テーマファイルをエクスポート", "Delete a theme": "テーマを削除する", "Update a theme file": "テーマファイルを更新", "Save as a new theme": "新しいテーマとして保存", "Avatar Style:": "アバタースタイル", "Circle": "円", "Square": "正方形", "Rectangle": "長方形", "Chat Style:": "チャットスタイル：", "Flat": "フラット\nバブル\nドキュメント", "Bubbles": "バブル", "Document": "文書", "Specify colors for your theme.": "テーマの色を指定します。", "Theme Colors": "テーマカラー", "Main Text": "メインテキスト", "Italics Text": "イタリックテキスト", "Underlined Text": "下線付きテキスト", "Quote Text": "引用テキスト", "Shadow Color": "シャドウカラー", "Chat Background": "チャットの背景", "UI Background": "UIの背景", "UI Border": "UIの境界", "User Message Blur Tint": "ユーザーメッセージのぼかし色", "AI Message Blur Tint": "AIメッセージのぼかし色", "Chat Width": "チャットの幅", "Width of the main chat window in % of screen width": "メインチャットウィンドウの幅（画面幅に対する％）", "Font Scale": "フォントスケール", "Font size": "フォントサイズ", "Blur Strength": "ぼかしの強さ", "Blur strength on UI panels.": "UI パネルのぼかしの強度。", "Text Shadow Width": "テキストシャドウの幅", "Strength of the text shadows": "テキストの影の強さ", "Disables animations and transitions": "アニメーションとトランジションを無効にする", "Reduced Motion": "動作の軽減", "removes blur from window backgrounds": "ウィンドウの背景のぼかしを除去する", "No Blur Effect": "ぼかし効果なし", "Remove text shadow effect": "テキストの影の効果を削除する", "No Text Shadows": "テキストシャドウなし", "Reduce chat height, and put a static sprite behind the chat window": "チャットの高さを縮小し、チャットウィンドウの背後に静的なスプライトを配置する", "Waifu Mode": "ワイフモード", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "チャットメッセージのメッセージアクションコンテキスト項目の完全なリストを常に表示し、それらを '...' の後ろに隠す代わりに", "Auto-Expand Message Actions": "メッセージアクションを自動展開する", "Alternative UI for numeric sampling parameters with fewer steps": "より少ないステップで数値サンプリングパラメーターの代替UI", "Zen Sliders": "禅のスライダー", "Entirely unrestrict all numeric sampling parameters": "すべての数値サンプリングパラメーターの制限を完全に解除する", "Mad Lab Mode": "Mad Labモード", "Time the AI's message generation, and show the duration in the chat log": "AIのメッセージ生成の時間を計測し、チャットログにその期間を表示する", "Message Timer": "メッセージタイマー", "Show a timestamp for each message in the chat log": "チャットログ内の各メッセージにタイムスタンプを表示する", "Chat Timestamps": "チャットのタイムスタンプ", "Show an icon for the API that generated the message": "メッセージを生成したAPIのアイコンを表示する", "Model Icon": "モデルアイコン", "Show sequential message numbers in the chat log": "チャットログに連続したメッセージ番号を表示する", "Message IDs": "メッセージID", "Hide avatars in chat messages.": "チャットメッセージ内のアバターを非表示にします。", "Hide Chat Avatars": "チャットアバターを非表示", "Show the number of tokens in each message in the chat log": "チャットログ内の各メッセージのトークン数を表示する", "Show Message Token Count": "メッセージトークンの数を表示", "Single-row message input area. Mobile only, no effect on PC": "一行のメッセージ入力エリア。モバイル専用で、PCには影響がありません", "Compact Input Area (Mobile)": "コンパクトな入力エリア（モバイル）", "In the Character Management panel, show quick selection buttons for favorited characters": "キャラクター管理パネルで、お気に入りのキャラクター用の迅速な選択ボタンを表示する", "Characters Hotswap": "キャラクターホットスワップ", "Enable magnification for zoomed avatar display.": "ズームされたアバター表示の拡大を有効にします。", "Avatar Hover Magnification": "アバターホバー拡大", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "チャットでアバターの画像をクリックした後に拡大されたアバターを表示するときに、ホバー時に拡大効果を有効にします。", "Show tagged character folders in the character list": "キャラクターリストでタグ付きキャラクターフォルダーを表示する", "Tags as Folders": "タグをフォルダーとして", "Tags_as_Folders_desc": "最近の変更: タグは、タグ管理メニューでフォルダーとしてマークされて初めてフォルダーとして表示されます。ここをクリックして表示します。", "Character Handling": "キャラクター処理", "If set in the advanced character definitions, this field will be displayed in the characters list.": "高度なキャラクター定義で設定されている場合、このフィールドがキャラクターリストに表示されます。", "Char List Subheader": "キャラクターリストサブヘッダー", "Character Version": "キャラクターバージョン", "Created by": "作成者", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "曖昧な一致を使用し、名前の部分文字列ではなく、すべてのデータフィールドでリスト内のキャラクターを検索する", "Advanced Character Search": "高度なキャラクター検索", "If checked and the character card contains a prompt override (System Prompt), use that instead": "チェックされていてキャラクターカードにプロンプトオーバーライド（システムプロンプト）が含まれている場合、それを代わりに使用します", "Prefer Character Card Prompt": "キャラクターカードのプロンプトを優先", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "チェックされていてキャラクターカードにジェイルブレイクオーバーライド（投稿履歴指示）が含まれている場合、それを代わりに使用します", "Prefer Character Card Jailbreak": "キャラクターカードのJailbreakを優先", "never_resize_avatars_tooltip": "インポートした文字画像の切り取りやサイズ変更を避けます。オフにすると、512x768 に切り取り/サイズ変更されます。", "Never resize avatars": "アバターを常にリサイズしない", "Show actual file names on the disk, in the characters list display only": "ディスク上の実際のファイル名を表示します。キャラクターリストの表示にのみ", "Show avatar filenames": "アバターのファイル名を表示", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "キャラクターのインポート時に埋め込みカードタグのインポートを促します。それ以外の場合、埋め込みタグは無視されます", "Import Card Tags": "カードのタグをインポート", "Hide character definitions from the editor panel behind a spoiler button": "スポイラーボタンの後ろのエディターパネルからキャラクターの定義を非表示にします", "Spoiler Free Mode": "スポイラーフリーモード", "Miscellaneous": "その他", "Reload and redraw the currently open chat": "現在開いているチャットを再読み込みして再描画する", "Reload Chat": "チャットをリロード", "Debug Menu": "デバッグメニュー", "Smooth Streaming": "スムーズなストリーミング", "Experimental feature. May not work for all backends.": "実験的な機能です。すべてのバックエンドで動作するとは限りません。", "Slow": "遅い", "Fast": "速い", "Play a sound when a message generation finishes": "メッセージ生成が終了したときに音を再生する", "Message Sound": "メッセージ音", "Only play a sound when ST's browser tab is unfocused": "STのブラウザタブがフォーカスされていない場合にのみ音を再生する", "Background Sound Only": "背景音のみ", "Reduce the formatting requirements on API URLs": "APIのURLの書式要件を緩和する", "Relaxed API URLS": "リラックスしたAPI URLS", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "埋め込み式の伝説の書を持つすべての新しいキャラクターのためにWorld Info/Lorebookをインポートするように問い合わせます。チェックされていない場合、代わりに簡潔なメッセージが表示されます", "Lorebook Import Dialog": "Lorebookインポートダイアログ", "Restore unsaved user input on page refresh": "ページをリフレッシュすると、保存されていないユーザー入力を復元する", "Restore User Input": "ユーザー入力の復元", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "ドラッグして特定のUI要素の位置を変更できるようにする。PC専用で、モバイルには影響がありません", "Movable UI Panels": "移動可能なUIパネル", "MovingUI preset. Predefined/saved draggable positions": "MovingUIプリセット。事前定義/保存されたドラッグ可能な位置", "MUI Preset": "MUIプリセット", "Save movingUI changes to a new file": "移動中のUI変更を新しいファイルに保存する", "Reset MovingUI panel sizes/locations.": "MovingUI パネルのサイズ/位置をリセットします。", "Apply a custom CSS style to all of the ST GUI": "ST GUI全体にカスタムCSSスタイルを適用する", "Custom CSS": "カスタムCSS", "Expand the editor": "エディターを展開する", "Chat/Message Handling": "チャット/メッセージの処理", "# Messages to Load": "# 読み込むメッセージ", "The number of chat history messages to load before pagination.": "ページ区切りの前に読み込むチャット履歴メッセージの数。", "(0 = All)": "(0 = すべて)", "Streaming FPS": "ストリーミングFPS", "Update speed of streamed text.": "ストリーミングテキストの更新速度。", "Example Messages Behavior": "例のメッセージの振る舞い", "Gradual push-out": "徐々にプッシュアウト", "Always include examples": "常に例を含める", "Never include examples": "決して例を含めない", "Send on Enter": "Enterキーで送信", "Disabled": "無効", "Automatic (PC)": "自動（PC）", "Press Send to continue": "'送信'を押して続行", "Show a button in the input area to ask the AI to continue (extend) its last message": "入力エリアにボタンを表示して、AIに最後のメッセージを続行（延長）するように依頼します", "Quick 'Continue' button": "迅速な「続行」ボタン", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "最後のチャットメッセージに矢印ボタンを表示して、代替のAI応答を生成します。PCとモバイルの両方", "Swipes": "スワイプ", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "最後のチャットメッセージでスワイプジェスチャーを使用してスワイプ生成をトリガーできるようにします。モバイル専用で、PCには影響がありません", "Gestures": "ジェスチャー", "Auto-load Last Chat": "最後のチャットを自動読み込み", "Auto-scroll Chat": "チャットの自動スクロール", "Save edits to messages without confirmation as you type": "入力時に確認なしでメッセージの編集を保存する", "Auto-save Message Edits": "メッセージ編集の自動保存", "Confirm message deletion": "メッセージの削除を確認", "Auto-fix Markdown": "Markdownの自動修正", "Disallow embedded media from other domains in chat messages": "チャットメッセージの他のドメインからの埋め込みメディアを禁止する", "Forbid External Media": "外部メディアを禁止", "Allow {{char}}: in bot messages": "ボットメッセージ内の{{char}}:を許可", "Allow {{user}}: in bot messages": "ボットメッセージ内の{{user}}:を許可", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "メッセージテキスト内のエンコーディングおよび文字のスキップ。一部のHTMLマークアップおよびMarkdownを許可します", "Show tags in responses": "応答でタグを表示", "Allow AI messages in groups to contain lines spoken by other group members": "グループ内のAIメッセージに、他のグループメンバーによって話された行を含めることを許可する", "Relax message trim in Groups": "グループ内のメッセージトリムを緩和する", "Log prompts to console": "プロンプトをコンソールに記録", "Requests logprobs from the API for the Token Probabilities feature": "トークン確率機能のAPIからのlogprobsをリクエストします", "Request token probabilities": "トークンの確率を要求", "Automatically reject and re-generate AI message based on configurable criteria": "設定可能な基準に基づいてAIメッセージを自動的に拒否して再生成します", "Auto-swipe": "オートスワイプ", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "自動スワイプ機能を有効にします。このセクションの設定は、自動スワイプが有効になっている場合にのみ効果があります", "Minimum generated message length": "生成されたメッセージの最小長", "If the generated message is shorter than these many characters, trigger an auto-swipe": "生成されたメッセージがこれよりも短い場合、自動スワイプをトリガーします", "Blacklisted words": "ブラックリストされた単語", "words you dont want generated separated by comma ','": "コンマ ',' で区切られた生成したくない単語", "Blacklisted word count to swipe": "スワイプするブラックリストされた単語の数", "Minimum number of blacklisted words detected to trigger an auto-swipe": "オートスワイプをトリガーするために検出されたブラックリストされた単語の最小数", "AutoComplete Settings": "オートコンプリート設定", "Automatically hide details": "詳細を自動的に非表示にする", "Determines how entries are found for autocomplete.": "オートコンプリートのエントリの検索方法を決定します。", "Autocomplete Matching": "マッチング", "Starts with": "前方一致", "Includes": "部分一致", "Fuzzy": "ファジー", "Sets the style of the autocomplete.": "オートコンプリートのスタイルを設定します。", "Autocomplete Style": "スタイル", "Follow Theme": "テーマをフォロー", "Dark": "暗い", "Sets the font size of the autocomplete.": "オートコンプリートのフォント サイズを設定します。", "Sets the width of the autocomplete.": "オートコンプリートの幅を設定します。", "Autocomplete Width": "幅", "chat input box": "チャット入力ボックス", "entire chat width": "チャット全体の幅", "full window width": "ウィンドウ全体の幅", "STscript Settings": "STscript 設定", "Sets default flags for the STscript parser.": "STscript パーサーのデフォルト フラグを設定します。", "Parser Flags": "パーサーフラグ", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "より厳密なエスケープに切り替えて、すべての区切り文字をバックスラッシュでエスケープし、バックスラッシュもエスケープできるようにします。", "STRICT_ESCAPING": "厳格なエスケープ", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "二重のマクロ置換を回避するために、すべての {{getvar::}} および {{getglobalvar::}} マクロをスコープ付き変数に置き換えます。", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "背景画像を変更", "Filter": "フィルター", "Automatically select a background based on the chat context": "チャットのコンテキストに基づいて背景を自動選択", "Auto-select": "自動選択", "System Backgrounds": "システムの背景", "Chat Backgrounds": "チャットの背景", "bg_chat_hint_1": "", "bg_chat_hint_2": "拡張機能で生成したチャットの背景はここに表示されます。", "Extensions": "拡張機能", "Notify on extension updates": "拡張機能の更新時に通知", "Manage extensions": "拡張機能を管理", "Import Extension From Git Repo": "Gitリポジトリから拡張機能をインポート", "Install extension": "拡張機能をインストール", "Extras API:": "追加API:", "Auto-connect": "自動接続", "Extras API URL": "エクストラAPI URL", "Extras API key (optional)": "エクストラAPIキー（オプション）", "Persona Management": "ペルソナ管理", "How do I use this?": "これをどのように使用しますか？", "Click for stats!": "統計をクリック！", "Usage Stats": "使用状況統計", "Backup your personas to a file": "キャラクターをファイルにバックアップします", "Backup": "バックアップ", "Restore your personas from a file": "ファイルからキャラクターを復元します", "Restore": "復元", "Create a dummy persona": "ダミーのペルソナを作成", "Create": "作成", "Toggle grid view": "グリッドビューの切り替え", "No persona description": "[説明なし]", "Name": "名前", "Enter your name": "あなたの名前を入力してください", "Click to set a new User Name": "新しいユーザー名を設定するにはクリック", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "選択したペルソナを現在のチャットにロックするにはクリックします。 ロックを解除するには、もう一度クリックします。", "Click to set user name for all messages": "すべてのメッセージにユーザー名を設定するにはクリック", "Persona Description": "ペルソナの説明", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "例：[{{user}}は28歳のルーマニアの猫の少女です。]", "Tokens persona description": "トークン", "Position:": "位置：", "In Story String / Prompt Manager": "ストーリー文字列/プロンプトマネージャー", "Top of Author's Note": "作者の注意の上部", "Bottom of Author's Note": "作者の注意の下部", "In-chat @ Depth": "チャット内 @ Depth", "Depth:": "深さ:", "Role:": "役割：", "System": "システム", "User": "ユーザー", "Assistant": "アシスタント", "Show notifications on switching personas": "ペルソナの切り替え時に通知を表示する", "Character Management": "キャラクター管理", "Locked = Character Management panel will stay open": "ロックされました=キャラクター管理パネルは開いたままになります", "Select/Create Characters": "キャラクターを選択/作成", "Favorite characters to add them to HotSwaps": "お気に入りのキャラクターを選択してHotSwapsに追加", "Token counts may be inaccurate and provided just for reference.": "トークン数は不正確かもしれず、参考のために提供されます。", "Total tokens": "合計トークン", "Calculating...": "計算中...", "Tokens": "トークン", "Permanent tokens": "永久トークン", "Permanent": "永続", "About Token 'Limits'": "トークンの「制限」について", "Toggle character info panel": "キャラクター情報パネルを切り替える", "Name this character": "このキャラクターに名前を付ける", "extension_token_counter": "トークン:", "Click to select a new avatar for this character": "このキャラクターの新しいアバターを選択するにはクリック", "Add to Favorites": "お気に入りに追加", "Advanced Definition": "高度な定義", "Character Lore": "キャラクターロア", "Chat Lore": "チャットロア", "Export and Download": "エクスポートとダウンロード", "Duplicate Character": "キャラクターを複製", "Create Character": "キャラクターを作成", "Delete Character": "キャラクターを削除", "More...": "詳細...", "Link to World Info": "ワールド情報へのリンク", "Import Card Lore": "カードの伝説をインポート", "Scenario Override": "シナリオオーバーライド", "Convert to Persona": "ペルソナに変換", "Rename": "名前を変更", "Link to Source": "ソースへのリンク", "Replace / Update": "置き換え/更新", "Import Tags": "タグをインポート", "Search / Create Tags": "タグを検索/作成", "View all tags": "すべてのタグを表示", "Creator's Notes": "作者のメモ", "Show / Hide Description and First Message": "説明と最初のメッセージを表示/非表示", "Character Description": "キャラクターの説明", "Click to allow/forbid the use of external media for this character.": "このキャラクターの外部メディアの使用を許可/禁止するにはクリックします。", "Ext. Media": "外部メディア", "Describe your character's physical and mental traits here.": "ここにキャラクターの身体的および精神的特徴を説明します。", "First message": "最初のメッセージ", "Click to set additional greeting messages": "追加の挨拶メッセージを設定するにはクリック", "Alt. Greetings": "他の挨拶", "This will be the first message from the character that starts every chat.": "これはすべてのチャットを開始するキャラクターからの最初のメッセージになります。", "Group Controls": "グループコントロール", "Chat Name (Optional)": "チャット名（任意）", "Click to select a new avatar for this group": "このグループの新しいアバターを選択するにはクリック", "Group reply strategy": "グループ返信戦略", "Natural order": "自然な順序", "List order": "リストの順序", "Group generation handling mode": "グループ生成処理モード", "Swap character cards": "キャラクターカードの交換", "Join character cards (exclude muted)": "キャラクターカードに参加（ミュートは除く）", "Join character cards (include muted)": "キャラクターカードに参加（ミュート含む）", "Inserted before each part of the joined fields.": "結合されたフィールドの各部分の前に挿入されます。", "Join Prefix": "結合プレフィックス", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "「キャラクターカードを結合」を選択すると、キャラクターのそれぞれのフィールドがすべて結合されます。つまり、たとえばストーリー文字列では、すべてのキャラクターの説明が 1 つの大きなテキストに結合されます。これらのフィールドを分離したい場合は、ここでプレフィックスまたはサフィックスを定義できます。この値は通常のマクロをサポートし、{{char}} を関連するキャラクターの名前に置き換え、<FIELDNAME> をパーツの名前 (例: 説明、性格、シナリオなど) に置き換えます。", "Inserted after each part of the joined fields.": "結合されたフィールドの各部分の後に挿入されます。", "Join Suffix": "結合サフィックス", "Set a group chat scenario": "グループチャットのシナリオを設定", "Click to allow/forbid the use of external media for this group.": "このグループに対して外部メディアの使用を許可/禁止するにはクリックします。", "Restore collage avatar": "コラージュアバターを復元", "Allow self responses": "自己応答を許可する", "Auto Mode": "自動モード", "Auto Mode delay": "自動モードの遅延", "Hide Muted Member Sprites": "ミュートされたメンバーのスプライトを非表示にする", "Current Members": "現在のメンバー", "Add Members": "メンバーを追加する", "Create New Character": "新しいキャラクターを作成", "Import Character from File": "ファイルからキャラクターをインポート", "Import content from external URL": "外部URLからコンテンツをインポート", "Create New Chat Group": "新しいチャットグループを作成", "Characters sorting order": "キャラクターのソート順", "A-Z": "ア-ン", "Z-A": "ン-ア", "Newest": "最新", "Oldest": "最古", "Favorites": "お気に入り", "Recent": "最近", "Most chats": "最も多いチャット", "Least chats": "最も少ないチャット", "Most tokens": "最も多いトークン", "Least tokens": "最も少ないトークン", "Random": "ランダム", "Toggle character grid view": "キャラクターグリッドビューの切り替え", "Bulk_edit_characters": "キャラクターを一括編集", "Bulk select all characters": "すべての文字を一括選択", "Bulk delete characters": "キャラクターを一括削除", "popup-button-save": "保存", "popup-button-yes": "はい", "popup-button-no": "いいえ", "popup-button-cancel": "キャンセル", "popup-button-import": "インポート", "Advanced Definitions": "高度な定義", "Prompt Overrides": "プロンプトのオーバーライド", "(For Chat Completion and Instruct Mode)": "（チャット補完と指示モード用）", "Insert {{original}} into either box to include the respective default prompt from system settings.": "システム設定からの対応するデフォルトのプロンプトを含めるには、どちらかのボックスに{{original}}を挿入します。", "Main Prompt": "メインプロンプト", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "ここにあるコンテンツは、このキャラクターに使用されるデフォルトのメインプロンプトを置き換えます。（v2仕様：system_prompt）", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "ここにあるコンテンツは、このキャラクターに使用されるデフォルトのジェイルブレイクプロンプトを置き換えます。（v2仕様：post_history_instructions）", "Creator's Metadata (Not sent with the AI prompt)": "作成者のメタデータ（AIプロンプトとは送信されません）", "Creator's Metadata": "クリエイターのメタデータ", "(Not sent with the AI Prompt)": "(AIプロンプトでは送信されません)", "Everything here is optional": "ここにあるすべては任意です", "(Botmaker's name / Contact Info)": "（ボットメーカーの名前/連絡先情報）", "(If you want to track character versions)": "（キャラクターバージョンを追跡したい場合）", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "（ボットを説明し、使用のヒントを与えるか、それがテストされたチャットモデルのリストを示します。これはキャラクターリストに表示されます。）", "Tags to Embed": "埋め込むタグ", "(Write a comma-separated list of tags)": "（カンマで区切られたタグのリストを書きます）", "Personality summary": "人格の要約", "(A brief description of the personality)": "（人格の簡単な説明）", "Scenario": "<PERSON><PERSON>リオ", "(Circumstances and context of the interaction)": "（相互作用の状況と文脈）", "Character's Note": "キャラクターノート", "(Text to be inserted in-chat @ designated depth and role)": "(指定された深さと役割でチャットに挿入されるテキスト)", "@ Depth": "@ 深さ", "Role": "役割", "Talkativeness": "話し好き", "How often the character speaks in group chats!": "キャラクターがグループチャットで話す頻度！", "How often the character speaks in": "キャラクターが話す頻度", "group chats!": "グループチャット！", "Shy": "シャイ", "Normal": "普通", "Chatty": "おしゃべり", "Examples of dialogue": "対話の例", "Important to set the character's writing style.": "キャラクターの執筆スタイルを設定するのに重要です。", "(Examples of chat dialog. Begin each example with START on a new line.)": "（チャットダイアログの例。各例を新しい行でSTARTで始めます。）", "Save": "保存", "Chat History": "チャット履歴", "Import Chat": "チャットをインポート", "Copy to system backgrounds": "システム背景にコピー", "Rename background": "背景の名前を変更", "Lock": "ロック", "Unlock": "ロック解除", "Delete background": "背景を削除", "Chat Scenario Override": "チャットシナリオのオーバーライド", "Remove": "削除", "Type here...": "ここに入力...", "Chat Lorebook": "チャットロアブック", "Chat Lorebook for": "チャットロアブック", "chat_world_template_txt": "選択したワールド情報はこのチャットにバインドされます。AI の返信を生成する際、\nグローバルおよびキャラクターのロアブックのエントリと結合されます。", "Select a World Info file for": "次のためにワールド情報ファイルを選択", "Primary Lorebook": "プライマリロアブック", "A selected World Info will be bound to this character as its own Lorebook.": "選択したワールド情報は、このキャラクターにその独自のロアブックとしてバインドされます。", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "AI応答を生成する際、グローバルワールド情報セレクターのエントリと組み合わされます。", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "キャラクターをエクスポートすると、JSONデータに埋め込まれた選択したロアブックファイルもエクスポートされます。", "Additional Lorebooks": "追加のロアブック", "Associate one or more auxillary Lorebooks with this character.": "このキャラクターに1つ以上の補助ロアブックを関連付けます。", "NOTE: These choices are optional and won't be preserved on character export!": "注意：これらの選択肢は任意であり、キャラクターエクスポート時には保存されません！", "Rename chat file": "チャットファイルの名前を変更", "Export JSONL chat file": "JSONLチャットファイルをエクスポート", "Download chat as plain text document": "プレーンテキストドキュメントとしてチャットをダウンロード", "Delete chat file": "チャットファイルを削除", "Use tag as folder": "フォルダとしてタグ付け", "Hide on character card": "キャラクターカードで非表示", "Delete tag": "タグを削除", "Entry Title/Memo": "エントリータイトル/メモ", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "WI エントリ ステータス: 🔵 定数 🟢 通常 🔗 ベクトル化 ❌ 無効", "WI_Entry_Status_Constant": "絶え間ない", "WI_Entry_Status_Normal": "普通", "WI_Entry_Status_Vectorized": "ベクトル化された", "WI_Entry_Status_Disabled": "無効", "T_Position": "↑キャラ：キャラクター定義の前\n↓キャラ：キャラクター定義の後\n↑AN：著者のメモの前\n↓AN：著者のメモの後\n@D：深さ", "Before Char Defs": "キャラクター定義の前", "After Char Defs": "キャラクター定義の後", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "著者のメモの前", "After AN": "著者のメモの後", "at Depth System": "指定深度(システム)", "at Depth User": "指定深度(ユーザー)", "at Depth AI": "指定深度(AI)", "Depth": "深さ", "Order:": "順序:", "Order": "順序：", "Trigger %:": "発生 ％：", "Probability": "確率", "Duplicate world info entry": "重複したワールド情報エントリ", "Delete world info entry": "世界情報エントリを削除", "Comma separated (required)": "カンマで区切られています（必須）", "Primary Keywords": "主要キーワード", "Keywords or Regexes": "キーワードまたは正規表現", "Comma separated list": "カンマ区切りリスト", "Switch to plaintext mode": "プレーンテキストモードに切り替える", "Logic": "論理", "AND ANY": "AND ANY", "AND ALL": "AND ALL", "NOT ALL": "NOT ALL", "NOT ANY": "NOT ANY", "(ignored if empty)": "(空の場合は無視されます)", "Optional Filter": "オプションフィルタ", "Keywords or Regexes (ignored if empty)": "キーワードまたは正規表現（空の場合は無視されます）", "Comma separated list (ignored if empty)": "カンマ区切りのリスト（空の場合は無視されます）", "Use global setting": "グローバル設定を使用", "Case-Sensitive": "大文字と小文字を区別する", "Yes": "はい", "No": "いいえ", "Can be used to automatically activate Quick Replies": "クイック返信を自動的に有効にするために使用できます", "Automation ID": "自動化ID", "( None )": "（ なし ）", "Content": "内容", "Exclude from recursion": "再帰から除外", "Prevent further recursion (this entry will not activate others)": "それ以上の再帰を防止します（このエントリは他のエントリをアクティブにしません）", "Delay until recursion (this entry can only be activated on recursive checking)": "再帰まで遅延 (このエントリは再帰チェックでのみ有効になります)", "What this keyword should mean to the AI, sent verbatim": "このキーワードがAIにとって何を意味するか、そのまま送信されます", "Filter to Character(s)": "キャラクターにフィルター", "Character Exclusion": "キャラクターの除外", "-- Characters not found --": "-- キャラクターが見つかりません --", "Inclusion Group": "包含グループ", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "包含グループにより、複数のエントリがトリガーされた場合、一度に 1 つのグループから 1 つのエントリのみがアクティブ化されます。カンマで区切られた複数のグループをサポートします。ドキュメント: World Info - 包含グループ", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "このエントリを優先する: チェックすると、このエントリがすべての選択項目の中で優先されます。複数のエントリを優先する場合は、「順序」が最も高いエントリが選択されます。", "Only one entry with the same label will be activated": "同じラベルのエントリが1つだけ有効になります", "A relative likelihood of entry activation within the group": "グループ内でのエントリー活性化の相対的な可能性", "Group Weight": "グループの重み", "Selective": "選択的", "Use Probability": "確率を使用", "Add Memo": "メモを追加", "Text or token ids": "テキストまたは[トークンID]", "close": "閉じる", "prompt_manager_edit": "編集", "prompt_manager_name": "名前", "A name for this prompt.": "このプロンプトの名前。", "To whom this message will be attributed.": "このメッセージの送信者。", "AI Assistant": "AIアシスタント", "prompt_manager_position": "位置", "Next to other prompts (relative) or in-chat (absolute).": "他のプロンプトの隣 (相対) またはチャット内 (絶対)。", "prompt_manager_relative": "相対的", "prompt_manager_depth": "深さ", "0 = after the last message, 1 = before the last message, etc.": "0 = 最後のメッセージの後、1 = 最後のメッセージの前など。", "Prompt": "プロンプト", "The prompt to be sent.": "送信されるプロンプト。", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "このプロンプトは、オーバーライドが優先される場合でも、キャラクター カードによってオーバーライドすることはできません。", "prompt_manager_forbid_overrides": "オーバーライドを禁止する", "reset": "リセット", "save": "保存", "This message is invisible for the AI": "このメッセージはAIには見えません", "Message Actions": "メッセージアクション", "Translate message": "メッセージを翻訳", "Generate Image": "画像を生成", "Narrate": "語る", "Exclude message from prompts": "プロンプトからメッセージを除外", "Include message in prompts": "プロンプトにメッセージを含める", "Embed file or image": "ファイルまたは画像を埋め込む", "Create checkpoint": "チェックポイントを作成", "Create Branch": "ブランチを作成", "Copy": "コピー", "Open checkpoint chat": "チェックポイントチャットを開く", "Edit": "編集", "Confirm": "確認", "Copy this message": "このメッセージをコピー", "Delete this message": "このメッセージを削除", "Move message up": "メッセージを上に移動", "Move message down": "メッセージを下に移動", "Enlarge": "拡大", "Welcome to SillyTavern!": "SillyTavern へようこそ!", "welcome_message_part_1": "読む", "welcome_message_part_2": "公式ドキュメント", "welcome_message_part_3": "。", "welcome_message_part_4": "タイプ", "welcome_message_part_5": "コマンドとマクロについてはチャットで。", "welcome_message_part_6": "参加する", "Discord server": "Discordサーバー", "welcome_message_part_7": "情報とお知らせ。", "SillyTavern is aimed at advanced users.": "SillyTavern は上級ユーザーを対象としています。", "If you're new to this, enable the simplified UI mode below.": "初めての場合は、以下の簡易 UI モードを有効にしてください。", "Change it later in the 'User Settings' panel.": "後で「ユーザー設定」パネルで変更します。", "Enable simple UI mode": "シンプルUIモードを有効にする", "Looking for AI characters?": "AIキャラクターをお探しですか?", "onboarding_import": "インポート", "from supported sources or view": "サポートされているソースからまたは表示", "Sample characters": "サンプルキャラクター", "Your Persona": "あなたのペルソナ", "Before you get started, you must select a persona name.": "始める前に、ペルソナ名を選択する必要があります。", "welcome_message_part_8": "これはいつでも変更可能です。", "welcome_message_part_9": "アイコン。", "Persona Name:": "ペルソナ名:", "Temporarily disable automatic replies from this character": "このキャラクターからの自動返信を一時的に無効にする", "Enable automatic replies from this character": "このキャラクターからの自動返信を有効にする", "Trigger a message from this character": "このキャラクターからメッセージをトリガーする", "Move up": "上に移動", "Move down": "下に移動", "View character card": "キャラクターカードを表示", "Remove from group": "グループから削除", "Add to group": "グループに追加", "Alternate Greetings": "挨拶のバリエーション", "Alternate_Greetings_desc": "これらは、新しいチャットを開始するときに最初のメッセージにスワイプとして表示されます。\nグループのメンバーは、そのうちの 1 つを選択して会話を開始できます。", "Alternate Greetings Hint": "ボタンをクリックして始めましょう!", "(This will be the first message from the character that starts every chat)": "（これはすべてのチャットを開始するキャラクターからの最初のメッセージになります）", "Forbid Media Override explanation": "現在のキャラクター/グループがチャットで外部メディアを使用できるかどうか。", "Forbid Media Override subtitle": "メディア: 画像、ビデオ、オーディオ。外部: ローカル サーバーでホストされていません。", "Always forbidden": "常に禁止", "Always allowed": "常に許可されている", "View contents": "コンテンツを見る", "Remove the file": "ファイルを削除する", "Unique to this chat": "このチャットに固有の", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "チェックポイントは親からノートを継承し、その後個別に変更できます。", "Include in World Info Scanning": "世界情報スキャンに含める", "Before Main Prompt / Story String": "メインプロンプト/ストーリー文字列の前", "After Main Prompt / Story String": "メインプロンプト/ストーリー文字列の後", "as": "として", "Insertion Frequency": "挿入頻度", "(0 = Disable, 1 = Always)": "(0 = 無効、1 = 常に)", "User inputs until next insertion:": "次の挿入までのユーザー入力:", "Character Author's Note (Private)": "キャラクター作者メモ（非公開）", "Won't be shared with the character card on export.": "エクスポート時にキャラクターカードと共有されません。", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "このキャラクターの作者のメモとして自動的に追加されます。グループで使用されますが、グループチャットが開いているときは変更できません。", "Use character author's note": "キャラクター作者のメモを使用する", "Replace Author's Note": "著者のメモを置き換える", "Default Author's Note": "デフォルトの著者のメモ", "Will be automatically added as the Author's Note for all new chats.": "すべての新しいチャットの作成者のメモとして自動的に追加されます。", "Chat CFG": "チャット CFG", "1 = disabled": "1 = 無効", "write short replies, write replies using past tense": "短い返信を書く、過去形を使って返信を書く", "Positive Prompt": "肯定的なプロンプト", "Use character CFG scales": "キャラクターCFGスケールを使用する", "Character CFG": "キャラクターCFG", "Will be automatically added as the CFG for this character.": "このキャラクターの CFG として自動的に追加されます。", "Global CFG": "グローバルCFG", "Will be used as the default CFG options for every chat unless overridden.": "上書きされない限り、すべてのチャットのデフォルトの CFG オプションとして使用されます。", "CFG Prompt Cascading": "CFG プロンプト カスケード", "Combine positive/negative prompts from other boxes.": "他のボックスからの肯定的/否定的なプロンプトを組み合わせます。", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "たとえば、チャット、グローバル、およびキャラクターのボックスにチェックを入れると、すべてのネガティブプロンプトがコンマ区切りの文字列に結合されます。", "Always Include": "常に含めます", "Chat Negatives": "チャットのネガティブ", "Character Negatives": "キャラクターのネガティブ", "Global Negatives": "グローバルネガティブ", "Custom Separator:": "カスタムセパレーター:", "Insertion Depth:": "挿入深さ:", "Token Probabilities": "トークン確率", "Select a token to see alternatives considered by the AI.": "AI が検討した代替案を表示するには、トークンを選択します。", "Not connected to API!": "APIに接続されていません！", "Type a message, or /? for help": "メッセージを入力するか、/? でヘルプを表示してください", "Continue script execution": "スクリプトの実行を続行する", "Pause script execution": "スクリプトの実行を一時停止する", "Abort script execution": "スクリプトの実行を中止する", "Abort request": "要求を中止", "Continue the last message": "前回のメッセージを継続", "Send a message": "メッセージを送信", "Close chat": "チャットを閉じる", "Toggle Panels": "パネルを切り替える", "Back to parent chat": "親チャットに戻る", "Save checkpoint": "チェックポイントを保存", "Convert to group": "グループに変換", "Start new chat": "新しいチャットを開始", "Manage chat files": "チャットファイルを管理", "Delete messages": "メッセージを削除", "Regenerate": "再生成", "Ask AI to write your message for you": "AIにあなたのメッセージを書くように依頼", "Impersonate": "擬態する", "Continue": "続行", "Bind user name to that avatar": "ユーザー名をそのアバターにバインド", "Change persona image": "ペルソナ画像を変更", "Select this as default persona for the new chats.": "これを新しいチャットのデフォルトのペルソナとして選択します。", "Delete persona": "ペルソナを削除", "These characters are the winners of character design contests and have outstandable quality.": "これらのキャラクターはキャラクターデザインコンテストの優勝者であり、優れた品質を誇ります。", "Contest Winners": "コンテスト受賞者", "These characters are the finalists of character design contests and have remarkable quality.": "これらのキャラクターはキャラクターデザインコンテストのファイナリストであり、優れた品質を誇ります。", "Featured Characters": "注目キャラクター", "Attach a File": "ファイルを添付する", "Open Data Bank": "オープンデータバンク", "Enter a URL or the ID of a Fandom wiki page to scrape:": "スクレイピングする Fandom Wiki ページの URL または ID を入力してください:", "Examples:": "例:", "Example:": "例：", "Single file": "単一ファイル", "All articles will be concatenated into a single file.": "すべての記事は 1 つのファイルに連結されます。", "File per article": "記事ごとのファイル", "Each article will be saved as a separate file.": "推奨されません。各記事は個別のファイルとして保存されます。", "Data Bank": "データバンク", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "これらのファイルは、添付ファイルをサポートする拡張機能 (Vector Storage など) で使用できます。", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "サポートされているファイルの種類: プレーンテキスト、PDF、Markdown、HTML、EPUB。", "Drag and drop files here to upload.": "アップロードするには、ファイルをここにドラッグ アンド ドロップします。", "Date (Newest First)": "日付（新しい順）", "Date (Oldest First)": "日付（古い順）", "Name (A-Z)": "名前 (A-Z)", "Name (Z-A)": "名前 (Z-A)", "Size (Smallest First)": "サイズ（小さい順）", "Size (Largest First)": "サイズ（大きい順）", "Bulk Edit": "一括編集", "Select All": "すべて選択", "Select None": "なしを選択", "Global Attachments": "グローバル添付ファイル", "These files are available for all characters in all chats.": "これらのファイルは、すべてのチャットのすべてのキャラクターで使用できます。", "Character Attachments": "キャラクターアタッチメント", "These files are available the current character in all chats they are in.": "これらのファイルは、現在のキャラクターが参加しているすべてのチャットで利用できます。", "Saved locally. Not exported.": "ローカルに保存されました。エクスポートされません。", "Chat Attachments": "チャット添付ファイル", "These files are available to all characters in the current chat.": "これらのファイルは、現在のチャット内のすべてのキャラクターが利用できます。", "Enter a base URL of the MediaWiki to scrape.": "スクレイピングする MediaWiki のベース URL を入力します。", "Don't include the page name!": "ページ名を含めないでください。", "Enter web URLs to scrape (one per line):": "スクレイピングする Web URL を入力します (1 行に 1 つずつ):", "Enter a video URL to download its transcript.": "ビデオの URL または ID を入力して、そのトランスクリプトをダウンロードします。", "Expression API": "ローカル\nエクストラ\nLLM", "ext_sum_with": "要約:", "ext_sum_main_api": "メインAPI", "ext_sum_current_summary": "現在の概要:", "ext_sum_restore_previous": "前の状態に戻す", "ext_sum_memory_placeholder": "ここで要約が生成されます...", "Trigger a summary update right now.": "今すぐ要約", "ext_sum_force_text": "今すぐ要約", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "自動サマリー更新を無効にします。一時停止中は、サマリーはそのまま残ります。[今すぐサマリーを作成] ボタン (メイン API でのみ使用可能) を押すと、強制的に更新できます。", "ext_sum_pause": "一時停止", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "要約するテキストからワールド情報と著者のメモを省略します。メイン API を使用する場合にのみ効果があります。Extras API は常に WI/AN を省略します。", "ext_sum_no_wi_an": "WI/ANなし", "ext_sum_settings_tip": "要約プロンプト、挿入位置などを編集します。", "ext_sum_settings": "概要設定", "ext_sum_prompt_builder": "プロンプトビルダー", "ext_sum_prompt_builder_1_desc": "拡張機能は、まだ要約されていないメッセージを使用して独自のプロンプトを作成します。要約が生成されるまでチャットをブロックします。", "ext_sum_prompt_builder_1": "生、ブロック", "ext_sum_prompt_builder_2_desc": "拡張機能は、まだ要約されていないメッセージを使用して独自のプロンプトを構築します。要約の生成中はチャットをブロックしません。すべてのバックエンドがこのモードをサポートしているわけではありません。", "ext_sum_prompt_builder_2": "生の、非ブロッキング", "ext_sum_prompt_builder_3_desc": "拡張機能は通常のメインプロンプトビルダーを使用し、最後のシステムメッセージとしてサマリー要求を追加します。", "ext_sum_prompt_builder_3": "クラシック、ブロッキング", "Summary Prompt": "概要プロンプト", "ext_sum_restore_default_prompt_tip": "デフォルトのプロンプトを復元する", "ext_sum_prompt_placeholder": "このプロンプトは、要約の生成を要求するために AI に送信されます。{{words}} は「単語数」パラメータに解決されます。", "ext_sum_target_length_1": "ターゲット要約の長さ", "ext_sum_target_length_2": "（", "ext_sum_target_length_3": "単語）", "ext_sum_api_response_length_1": "API レスポンスの長さ", "ext_sum_api_response_length_2": "（", "ext_sum_api_response_length_3": "トークン）", "ext_sum_0_default": "0 = デフォルト", "ext_sum_raw_max_msg": "[生] リクエストあたりの最大メッセージ数", "ext_sum_0_unlimited": "0 = 無制限", "Update frequency": "更新頻度", "ext_sum_update_every_messages_1": "更新する", "ext_sum_update_every_messages_2": "メッセージ", "ext_sum_0_disable": "0 = 無効", "ext_sum_auto_adjust_desc": "チャットのメトリックに基づいて間隔を自動的に調整してみてください。", "ext_sum_update_every_words_1": "更新する", "ext_sum_update_every_words_2": "言葉", "ext_sum_both_sliders": "両方のスライダーがゼロ以外の場合、両方のスライダーがそれぞれの間隔でサマリー更新をトリガーします。", "ext_sum_injection_template": "インジェクションテンプレート", "ext_sum_memory_template_placeholder": "{{summary}} は現在の概要コンテンツに解決されます。", "ext_sum_injection_position": "注入位置", "How many messages before the current end of the chat.": "現在のチャット終了までのメッセージ数。", "ext_regex_title": "正規表現", "ext_regex_new_global_script": "+ グローバル", "ext_regex_new_scoped_script": "+ スコープ付き", "ext_regex_import_script": "インポート", "ext_regex_global_scripts": "グローバルスクリプト", "ext_regex_global_scripts_desc": "すべてのキャラクターで使用可能。ローカル設定に保存されます。", "ext_regex_scoped_scripts": "スコープ付きスクリプト", "ext_regex_scoped_scripts_desc": "このキャラクターのみ使用可能。カードデータに保存されます。", "Regex Editor": "正規表現エディタ", "Test Mode": "テストモード", "ext_regex_desc": "Regex は、正規表現を使用して文字列を検索/置換するツールです。詳細を知りたい場合は、タイトルの横にある [?] をクリックしてください。", "Input": "入力", "ext_regex_test_input_placeholder": "ここに入力...", "Output": "出力", "ext_regex_output_placeholder": "空の", "Script Name": "スクリプト名", "Find Regex": "正規表現を検索", "Replace With": "と置換する", "ext_regex_replace_string_placeholder": "正規表現検索から一致したテキストを含めるには {{match}} を使用し、キャプチャ グループには $1、$2 などを使用します。", "Trim Out": "トリムアウト", "ext_regex_trim_placeholder": "置換前に、正規表現の一致から不要な部分を全体的にトリミングします。各要素を Enter で区切ります。", "ext_regex_affects": "影響", "ext_regex_user_input": "ユーザー入力", "ext_regex_ai_output": "AI出力", "Slash Commands": "スラッシュコマンド", "ext_regex_min_depth_desc": "プロンプトまたはディスプレイに適用すると、少なくとも N レベルの深さのメッセージにのみ影響します。0 = 最後のメッセージ、1 = 最後から 2 番目のメッセージなど。WI エントリ @Depth と使用可能なメッセージ (非表示またはシステムではない) のみをカウントします。", "Min Depth": "最小深度", "ext_regex_min_depth_placeholder": "無制限", "ext_regex_max_depth_desc": "プロンプトまたはディスプレイに適用された場合、N レベル以下の深さのメッセージにのみ影響します。0 = 最後のメッセージ、1 = 最後から 2 番目のメッセージなど。WI エントリ @Depth と使用可能なメッセージ (非表示またはシステムではない) のみをカウントします。", "ext_regex_other_options": "その他のオプション", "Only Format Display": "フォーマット表示のみ", "ext_regex_only_format_prompt_desc": "チャット履歴は変更されず、リクエストが送信されたときのプロンプトのみが変更されます (生成時)。", "Only Format Prompt (?)": "フォーマットプロンプトのみ", "Run On Edit": "編集時に実行", "ext_regex_substitute_regex_desc": "実行する前に、Find Regex で {{macros}} を置き換えてください。", "Substitute Regex": "正規表現の置換", "ext_regex_import_target": "インポート先:", "ext_regex_disable_script": "スクリプトを無効にする", "ext_regex_enable_script": "スクリプトを有効にする", "ext_regex_edit_script": "スクリプトを編集", "ext_regex_move_to_global": "グローバルスクリプトに移動", "ext_regex_move_to_scoped": "スコープ付きスクリプトに移動する", "ext_regex_export_script": "エクスポートスクリプト", "ext_regex_delete_script": "スクリプトを削除", "Trigger Stable Diffusion": "安定した拡散を誘発する", "sd_Yourself": "あなた自身", "sd_Your_Face": "あなたの顔", "sd_Me": "自分", "sd_The_Whole_Story": "一部始終", "sd_The_Last_Message": "最後のメッセージ", "sd_Raw_Last_Message": "生の最後のメッセージ", "sd_Background": "背景", "Image Generation": "画像生成", "Stop Image Generation": "画像生成を停止", "Generate Caption": "画像説明を生成", "sd_refine_mode": "プロンプトを生成 API に送信する前に手動で編集できるようにする", "sd_refine_mode_txt": "生成前にプロンプ​​トを編集する", "sd_interactive_mode": "「猫の写真を送ってください」のようなメッセージを送信するときに、画像を自動的に生成します。", "sd_interactive_mode_txt": "インタラクティブモード", "sd_multimodal_captioning": "マルチモーダル キャプションを使用して、アバターに基づいてユーザーとキャラクターのポートレートのプロンプトを生成します。", "sd_multimodal_captioning_txt": "ポートレートにはマルチモーダルキャプションを使用する", "sd_expand": "テキスト生成モデルを使用してプロンプトを自動的に拡張する", "sd_expand_txt": "自動強化プロンプト", "sd_snap": "絶対ピクセル数を維持しながら、強制アスペクト比 (ポートレート、背景) で生成要求を最も近い既知の解像度にスナップします (SDXL に推奨)。", "sd_snap_txt": "スナップ自動調整解像度", "Source": "ソース", "sd_auto_url": "例: {{auto_url}}", "Authentication (optional)": "認証（オプション）", "Example: username:password": "例: ユーザー名:パスワード", "Important:": "重要：", "sd_auto_auth_warning_1": "SD Web UIを", "sd_auto_auth_warning_2": "フラグを指定して実行してください! サーバーは SillyTavern ホスト マシンからアクセスできる必要があります。", "sd_drawthings_url": "例: {{drawthings_url}}", "sd_drawthings_auth_txt": "UI で HTTP API スイッチを有効にして DrawThings アプリを実行します。サーバーは SillyTavern ホスト マシンからアクセスできる必要があります。", "sd_vlad_url": "例: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "サーバーは SillyTavern ホスト マシンからアクセスできる必要があります。", "Hint: Save an API key in AI Horde API settings to use it here.": "ヒント: ここで使用するには、AI Horde API 設定に API キーを保存してください。", "Allow NSFW images from Horde": "HordeからのNSFW画像を許可する", "Sanitize prompts (recommended)": "サニタイズプロンプト（推奨）", "Automatically adjust generation parameters to ensure free image generations.": "生成パラメータを自動的に調整して、自由な画像生成を保証します。", "Avoid spending Anlas": "アンラスの支出を避ける", "Opus tier": "(Opus レベル)", "View my Anlas": "私のAnlasを見る", "These settings only apply to DALL-E 3": "これらの設定はDALL-E 3にのみ適用されます", "Image Style": "画像スタイル", "Image Quality": "画質", "Standard": "標準", "HD": "高解像度", "sd_comfy_url": "例: {{comfy_url}}", "Open workflow editor": "ワークフローエディタを開く", "Create new workflow": "新しいワークフローを作成する", "Delete workflow": "ワークフローの削除", "Enhance": "強化する", "Refine": "リファイン", "Decrisper": "デクリスパー", "Sampling steps": "サンプリングステップ数", "Width": "幅", "Height": "高さ", "Resolution": "解像度", "Model": "モデル", "Sampling method": "サンプリング方法", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (すべてのサンプラーがサポートされているわけではありません)", "SMEA versions of samplers are modified to perform better at high resolution.": "SMEA バージョンのサンプラーは、高解像度でより優れたパフォーマンスを発揮するように変更されています。", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "SMEA サンプラーの DYN バリアントは、多くの場合、より多様な出力をもたらしますが、非常に高い解像度では失敗する可能性があります。", "DYN": "ダイナミック", "Scheduler": "スケジューラー", "Restore Faces": "顔の修復", "Hires. Fix": "高解像度補助", "Upscaler": "アップスケーラー", "Upscale by": "アップスケール倍率", "Denoising strength": "ノイズ除去の強さ", "Hires steps (2nd pass)": "高解像度でのステップ数", "Preset for prompt prefix and negative prompt": "プロンプトプレフィックスとネガティブプロンプトのプリセット", "Style": "スタイル", "Save style": "スタイルを保存", "Delete style": "スタイルを削除", "Common prompt prefix": "共通のプロンプトプレフィックス", "sd_prompt_prefix_placeholder": "生成されたプロンプトを挿入する場所を指定するには、{prompt}を使用します。", "Negative common prompt prefix": "共通のネガティブプロンプトプレフィックス", "Character-specific prompt prefix": "キャラクター固有のプロンプトプレフィックス", "Won't be used in groups.": "グループでは使用されません。", "sd_character_prompt_placeholder": "現在選択されているキャラクターを説明する特徴。共通のプロンプトプレフィックスの後に追加されます。\n例: 女性、緑の目、茶色の髪、ピンクのシャツ", "Character-specific negative prompt prefix": "キャラクター固有のネガティブプロンプトプレフィックス", "sd_character_negative_prompt_placeholder": "選択したキャラクターに表示されるべきではない特徴。共通のネガティブプロンプトプレフィックスの後に追加されます。\n例: ジュエリー、靴、メガネ", "Shareable": "共有可能", "Image Prompt Templates": "画像プロンプトテンプレート", "Vectors Model Warning": "チャットの途中でモデルを変更する場合は、ベクトルを消去することをお勧めします。そうしないと、標準以下の結果になります。", "Translate files into English before processing": "処理前にファイルを英語に翻訳する", "Manager Users": "ユーザーの管理", "New User": "新しいユーザー", "Status:": "状態：", "Created:": "作成した：", "Display Name:": "表示名：", "User Handle:": "ユーザーハンドル:", "Password:": "パスワード：", "Confirm Password:": "パスワードを認証する：", "This will create a new subfolder...": "これにより、/data/ ディレクトリに、ユーザーのハンドルをフォルダ名として持つ新しいサブフォルダが作成されます。", "Current Password:": "現在のパスワード：", "New Password:": "新しいパスワード：", "Confirm New Password:": "新しいパスワードを確認：", "Debug Warning": "このカテゴリの機能は上級ユーザー専用です。結果がわからない場合は何もクリックしないでください。", "Execute": "実行する", "Are you sure you want to delete this user?": "このユーザーを削除してもよろしいですか?", "Deleting:": "削除中:", "Also wipe user data.": "ユーザーデータも消去します。", "Warning:": "警告：", "This action is irreversible.": "この操作は元に戻せません。", "Type the user's handle below to confirm:": "確認するには、以下のユーザーのハンドルを入力してください。", "Import Characters": "キャラクターをインポートする", "Enter the URL of the content to import": "インポートするコンテンツの URL を入力します", "Supported sources:": "サポートされているソース:", "char_import_1": "Chub キャラクター （直接リンクまたはID）", "char_import_example": "例：", "char_import_2": "Chub ロアブック (直接リンクまたは ID)", "char_import_3": "JanitorAI キャラクター (直接リンクまたは UUID)", "char_import_4": "Pygmalion.chat キャラクター (直接リンクまたは UUID)", "char_import_5": "AICharacterCards.com キャラクター (直接リンクまたは ID)", "char_import_6": "直接PNGリンク（参照", "char_import_7": "許可されたホストの場合)", "char_import_8": "RisuRealm キャラクター (直接リンク)", "char_import_9": "Soulkyn キャラクター (直接リンク)", "Supports importing multiple characters.": "複数のキャラクターのインポートをサポートします。", "Write each URL or ID into a new line.": "各 URL または ID を新しい行に入力します。", "Export for character": "キャラクターのエクスポート", "Export prompts for this character, including their order.": "このキャラクターのプロンプトを順序も含めてエクスポートします。", "Export all": "すべてをエクスポート", "Export all your prompts to a file": "すべてのプロンプトをファイルにエクスポートする", "Insert prompt": "プロンプトを挿入", "Delete prompt": "プロンプトを削除", "Import a prompt list": "プロンプトリストをインポート", "Export this prompt list": "このプロンプトリストをエクスポート", "Reset current character": "現在のキャラクターをリセット", "New prompt": "新しいプロンプト", "Prompts": "プロンプト", "Total Tokens:": "総トークン数：", "prompt_manager_tokens": "トークン", "Are you sure you want to reset your settings to factory defaults?": "設定を工場出荷時のデフォルトにリセットしてもよろしいですか?", "Don't forget to save a snapshot of your settings before proceeding.": "続行する前に、設定のスナップショットを保存することを忘れないでください。", "Settings Snapshots": "設定スナップショット", "Record a snapshot of your current settings.": "現在の設定のスナップショットを記録します。", "Make a Snapshot": "スナップショットを作成する", "Restore this snapshot": "このスナップショットを復元する", "Hi,": "こんにちは、", "To enable multi-account features, restart the SillyTavern server with": "マルチアカウント機能を有効にするには、SillyTavernサーバーを再起動します。", "set to true in the config.yaml file.": "config.yaml ファイルで true に設定します。", "Account Info": "アカウント情報", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "ユーザー アバターを変更するには、下のボタンを使用するか、ペルソナ管理メニューでデフォルトのペルソナを選択します。", "Set your custom avatar.": "カスタムアバターを設定します。", "Remove your custom avatar.": "カスタムアバターを削除します。", "Handle:": "ハンドル：", "This account is password protected.": "このアカウントはパスワードで保護されています。", "This account is not password protected.": "このアカウントはパスワードで保護されていません。", "Account Actions": "アカウントアクション", "Change Password": "パスワードを変更する", "Manage your settings snapshots.": "設定のスナップショットを管理します。", "Download a complete backup of your user data.": "ユーザーデータの完全なバックアップをダウンロードします。", "Download Backup": "バックアップをダウンロード", "Danger Zone": "危険区域", "Reset your settings to factory defaults.": "設定を工場出荷時の状態にリセットします。", "Reset Settings": "設定をリセット", "Wipe all user data and reset your account to factory settings.": "すべてのユーザーデータを消去し、アカウントを工場出荷時の設定にリセットします。", "Reset Everything": "すべてをリセット", "Reset Code:": "リセットコード:", "Want to update?": "更新しますか？", "How to start chatting?": "チャットを開始する方法は？", "Click _space": "クリック", "and select a": "そして選択します ", "Chat API": "チャットAPI", "and pick a character.": "キャラクターを選択します。", "You can browse a list of bundled characters in the": "バンドルされているキャラクターのリストは、", "Download Extensions & Assets": "拡張機能とアセットをダウンロード", "menu within": "メニュー内", "Confused or lost?": "混乱していますか？迷っていますか？", "click these icons!": "これらのアイコンをクリックしてください！", "in the chat bar": "チャットバーで", "SillyTavern Documentation Site": "SillyTavernドキュメントサイト", "Extras Installation Guide": "エクストラインストールガイド", "Still have questions?": "まだ質問がありますか？", "Join the SillyTavern Discord": "SillyTavernのDiscordに参加", "Post a GitHub issue": "GitHubの問題を投稿", "Contact the developers": "開発者に連絡", "Stop Inspecting": "検査を停止", "Inspect Prompts": "プロンプトを検査", "Toggle prompt inspection": "プロンプト検査の切り替え"}