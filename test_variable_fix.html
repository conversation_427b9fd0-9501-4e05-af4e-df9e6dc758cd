<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>变量管理器修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>变量管理器修复测试</h1>
        <p>这个页面用于测试变量管理器中"已搬到文件夹里的变量还出现在根目录"的修复情况。</p>
        
        <div class="test-section">
            <h3>问题描述</h3>
            <div class="status info">
                <strong>原问题：</strong>当变量被移动到文件夹后，如果该变量被删除，它仍然会在根目录中显示，造成重复显示的问题。
            </div>
        </div>

        <div class="test-section">
            <h3>修复说明</h3>
            <div class="status success">
                <strong>修复内容：</strong>
                <ul>
                    <li>在渲染变量时，清理文件夹中不存在的变量引用</li>
                    <li>只将真实存在的变量标记为"在文件夹中"</li>
                    <li>自动更新文件夹设置，移除无效的变量引用</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>修复的核心逻辑</h3>
            <pre><code>// 修复前的问题代码：
folder.variables.forEach(varName => variablesInFolders.add(varName));
// 这会将所有文件夹中的变量名都标记为"在文件夹中"，不管变量是否真实存在

// 修复后的代码：
const existingVariables = folder.variables.filter(varName => variables.hasOwnProperty(varName));
if (existingVariables.length !== folder.variables.length) {
    folder.variables = existingVariables;
    foldersChanged = true;
}
existingVariables.forEach(varName => variablesInFolders.add(varName));
// 只将真实存在的变量标记为"在文件夹中"，并清理无效引用</code></pre>
        </div>

        <div class="test-section">
            <h3>测试步骤</h3>
            <div class="status info">
                <ol>
                    <li>重新加载 SillyTavern</li>
                    <li>打开变量管理器</li>
                    <li>检查是否还有重复显示的变量</li>
                    <li>创建新变量并移动到文件夹</li>
                    <li>删除该变量，确认它不会在根目录重复出现</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>预期结果</h3>
            <div class="status success">
                <ul>
                    <li>✅ 已删除的变量不会在任何地方显示</li>
                    <li>✅ 文件夹中的变量不会在根目录重复显示</li>
                    <li>✅ 文件夹设置会自动清理无效的变量引用</li>
                    <li>✅ 变量的显示状态与实际存在状态保持一致</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>操作说明</h3>
            <button onclick="window.close()">关闭测试页面</button>
            <button onclick="location.reload()">刷新页面</button>
        </div>
    </div>

    <script>
        console.log('变量管理器修复测试页面已加载');
        console.log('请按照测试步骤验证修复效果');
    </script>
</body>
</html>
