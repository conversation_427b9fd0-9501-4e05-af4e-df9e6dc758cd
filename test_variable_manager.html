<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Variable Manager Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .inline-drawer {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .inline-drawer-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .inline-drawer-content {
            padding: 15px;
            display: none;
        }
        
        .inline-drawer.active .inline-drawer-content {
            display: block;
        }
        
        .flex-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .menu_button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .menu_button:hover {
            background: #0056b3;
        }
        
        .vm-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 1000px;
            height: 80%;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
        }
        
        .vm-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .vm-close {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
        }
        
        .test-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-button:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Variable Manager 功能测试</h1>
        
        <div id="extensions_settings2">
            <!-- 这里会插入设置面板 -->
        </div>
        
        <div class="status" id="status">
            状态：等待初始化...
        </div>
        
        <div class="test-buttons">
            <button class="test-button" onclick="testCheckbox()">测试复选框切换</button>
            <button class="test-button" onclick="testOpenButton()">测试打开按钮</button>
            <button class="test-button" onclick="testCloseButton()">测试关闭按钮</button>
            <button class="test-button" onclick="checkSlashCommand()">检查斜杠命令</button>
            <button class="test-button" onclick="checkContainerExists()">检查容器状态</button>
        </div>
    </div>

    <!-- 模拟必要的全局对象和函数 -->
    <script>
        // 模拟全局对象
        window.extension_settings = { variables: { global: {} } };
        window.chat_metadata = { variables: {} };
        window.toastr = {
            success: (msg) => console.log('SUCCESS:', msg),
            error: (msg) => console.log('ERROR:', msg),
            warning: (msg) => console.log('WARNING:', msg)
        };
        
        // 模拟函数
        window.saveSettingsDebounced = () => console.log('Settings saved');
        window.getContext = () => ({});
        window.eventSource = { on: () => {}, off: () => {} };
        window.event_types = {};
        
        // 模拟变量函数
        window.getLocalVariable = (name) => window.chat_metadata.variables[name];
        window.setLocalVariable = (name, value) => window.chat_metadata.variables[name] = value;
        window.getGlobalVariable = (name) => window.extension_settings.variables.global[name];
        window.setGlobalVariable = (name, value) => window.extension_settings.variables.global[name] = value;
        
        // 模拟斜杠命令解析器
        window.SlashCommandParser = {
            commands: {},
            addCommandObject: function(command) {
                this.commands[command.name] = command;
                console.log('Registered slash command:', command.name);
            }
        };
        
        // 模拟斜杠命令相关类
        window.SlashCommand = {
            fromProps: (props) => props
        };
        window.SlashCommandArgument = {
            fromProps: (props) => props
        };
        window.ARGUMENT_TYPE = {
            STRING: 'string'
        };
        
        // 测试函数
        function testCheckbox() {
            const checkbox = document.getElementById('variable_manager_enabled');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('input'));
                updateStatus(`复选框状态: ${checkbox.checked ? '启用' : '禁用'}`);
            }
        }
        
        function testOpenButton() {
            const button = document.getElementById('open_variable_manager');
            if (button) {
                button.click();
                updateStatus('点击了打开按钮');
            }
        }
        
        function testCloseButton() {
            const closeBtn = document.getElementById('vm-close');
            if (closeBtn) {
                closeBtn.click();
                updateStatus('点击了关闭按钮');
            } else {
                updateStatus('关闭按钮不存在（容器可能未创建）');
            }
        }
        
        function checkSlashCommand() {
            const hasCommand = 'xbgetvar' in window.SlashCommandParser.commands;
            updateStatus(`斜杠命令 /xbgetvar ${hasCommand ? '已注册' : '未注册'}`);
        }
        
        function checkContainerExists() {
            const container = document.getElementById('vm-container');
            const exists = container !== null;
            const visible = exists && container.style.display !== 'none';
            updateStatus(`容器状态: ${exists ? '存在' : '不存在'}, ${visible ? '可见' : '隐藏'}`);
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = '状态：' + message;
        }
        
        // 简单的drawer切换功能
        $(document).on('click', '.inline-drawer-header', function() {
            $(this).closest('.inline-drawer').toggleClass('active');
        });
    </script>
    
    <!-- 加载Variable Manager -->
    <script type="module">
        // 这里需要加载实际的Variable Manager代码
        // 由于模块导入的限制，我们需要手动复制代码或使用其他方式
        console.log('准备加载Variable Manager...');
        updateStatus('正在加载Variable Manager...');
    </script>
</body>
</html>
