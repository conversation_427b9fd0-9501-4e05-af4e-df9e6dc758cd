# SillyTavern 插件开发字典大全

## 基础概念

### 插件类型

* **global**: 全局插件，所有用户可用
* **local**: 本地插件，仅当前用户可用
* **system**: 系统内建插件，随应用更新

### 插件位置

```
data/<user-handle>/extensions/  # 本地插件
scripts/extensions/third-party/  # 第三方插件
scripts/extensions/             # 系统插件
```

---

## 📁 文件结构

### 必需文件

#### manifest.json

```json
{
    "display_name": "插件显示名称",
    "loading_order": 1,
    "requires": [],
    "optional": [],
    "js": "index.js",
    "css": "style.css",
    "author": "作者名称",
    "version": "1.0.0",
    "homePage": "https://github.com/your/extension",
    "auto_update": true,
    "i18n": {
        "de-de": "i18n/de-de.json"
    },
    "generate_interceptor": "myInterceptorFunction"
}
```

**字段说明:**

* ​`display_name`​: **必需** - 在扩展管理器中显示的名称
* ​`loading_order`​: **可选** - 加载顺序，数字越大越晚加载
* ​`requires`​: **可选** - 必需的Extras模块依赖
* ​`optional`​: **可选** - 可选的Extras依赖
* ​`js`​: **必需** - 主JavaScript文件路径
* ​`css`​: **可选** - 样式文件路径
* ​`author`​: **必需** - 作者信息
* ​`auto_update`​: **可选** - 是否自动更新
* ​`i18n`​: **可选** - 国际化文件映射
* ​`generate_interceptor`​: **可选** - 生成拦截器函数名

#### index.js (主脚本文件)

```javascript
// 基本导入
import { extension_settings, getContext } from "../../../extensions.js";
import { saveSettingsDebounced } from "../../../../script.js";

// 插件标识
const extensionName = "your-extension-name";
const extensionFolderPath = `scripts/extensions/third-party/${extensionName}`;

// 默认设置
const defaultSettings = {
    enabled: false,
    option1: 'default',
    option2: 5
};

// 初始化函数
jQuery(async () => {
    // 插件初始化代码
});
```

#### style.css (样式文件)

```css
/* 插件专用样式 */
.your-extension-class {
    /* 样式定义 */
}
```

### 可选文件

* **example.html**: UI模板文件
* **i18n/*.json**: 国际化翻译文件
* **README.md**: 说明文档

---

## 🔧 核心API

### getContext() 函数

获取SillyTavern主要上下文对象:

```javascript
const context = SillyTavern.getContext();

// 主要属性
context.chat;                    // 当前聊天消息数组
context.characters;              // 角色列表
context.characterId;             // 当前角色ID
context.groups;                  // 群组列表
context.extensionSettings;       // 扩展设置对象
context.saveSettingsDebounced;   // 防抖保存设置函数
context.addLocaleData;           // 添加本地化数据
context.renderTemplate;          // 渲染模板
context.getTokenCount;           // 获取token数量
```

### 导入其他模块

```javascript
// 从主脚本导入
import { 
    eventSource, 
    event_types,
    saveSettingsDebounced,
    characters,
    chat,
    this_chid
} from "../../../../script.js";

// 从扩展系统导入
import { 
    getApiUrl, 
    doExtrasFetch,
    extension_settings,
    getContext
} from "../../../extensions.js";
```

---

## 📡 事件系统

### 完整事件类型列表

```javascript
import { eventSource, event_types } from "../../../../script.js";

// 应用程序事件
event_types.APP_READY                           // 'app_ready' - 应用准备就绪
// 触发时机: SillyTavern完全加载并初始化完成后触发，是插件开始工作的最佳时机

event_types.SETTINGS_LOADED                     // 'settings_loaded' - 设置已加载
// 触发时机: 用户设置从本地存储加载完成后触发

event_types.SETTINGS_UPDATED                    // 'settings_updated' - 设置已更新
// 触发时机: 任何设置项被修改并保存时触发

event_types.SETTINGS_LOADED_BEFORE              // 'settings_loaded_before' - 设置加载前
// 触发时机: 开始加载设置之前触发，可用于预处理

event_types.SETTINGS_LOADED_AFTER               // 'settings_loaded_after' - 设置加载后
// 触发时机: 设置加载完成后触发，可用于后处理

// 消息事件
event_types.MESSAGE_SENT                        // 'message_sent' - 消息已发送
// 触发时机: 用户发送消息后触发，包含消息内容和元数据

event_types.MESSAGE_RECEIVED                    // 'message_received' - 消息已接收
// 触发时机: 收到AI回復消息后触发，包含完整的消息对象

event_types.MESSAGE_EDITED                      // 'message_edited' - 消息已编辑
// 触发时机: 用户编辑现有消息后触发

event_types.MESSAGE_DELETED                     // 'message_deleted' - 消息已删除
// 触发时机: 消息被删除时触发，包含被删除消息的信息

event_types.MESSAGE_UPDATED                     // 'message_updated' - 消息已更新
// 触发时机: 消息内容或元数据被更新时触发

event_types.MESSAGE_SWIPED                      // 'message_swiped' - 消息已滑动
// 触发时机: 用户滑动查看消息的不同变体时触发

event_types.MESSAGE_FILE_EMBEDDED               // 'message_file_embedded' - 文件已嵌入消息
// 触发时机: 文件被嵌入到消息中时触发

// 聊天事件
event_types.CHAT_CHANGED                        // 'chat_id_changed' - 聊天已切换
// 触发时机: 用户切换到不同的聊天会话时触发

event_types.CHAT_CREATED                        // 'chat_created' - 聊天已创建
// 触发时机: 新的聊天会话被创建时触发

event_types.CHAT_DELETED                        // 'chat_deleted' - 聊天已删除
// 触发时机: 聊天会话被删除时触发

// 角色事件
event_types.CHARACTER_EDITED                    // 'character_edited' - 角色已编辑
// 触发时机: 角色信息被修改并保存时触发

event_types.CHARACTER_DELETED                   // 'characterDeleted' - 角色已删除
// 触发时机: 角色被删除时触发

event_types.CHARACTER_DUPLICATED                // 'character_duplicated' - 角色已複製
// 触发时机: 角色被複製时触发

event_types.CHARACTER_PAGE_LOADED               // 'character_page_loaded' - 角色页面已加载
// 触发时机: 角色管理页面加载完成时触发

event_types.CHARACTER_MESSAGE_RENDERED          // 'character_message_rendered' - 角色消息已渲染
// 触发时机: 角色的消息在UI中渲染完成后触发

event_types.CHARACTER_FIRST_MESSAGE_SELECTED    // 'character_first_message_selected' - 首条消息已选择
// 触发时机: 角色的第一条消息被选中时触发

event_types.CHARACTER_GROUP_OVERLAY_STATE_CHANGE_BEFORE  // 'character_group_overlay_state_change_before' - 角色群组复盖状态变更前
// 触发时机: 角色群组复盖层状态改变之前触发

event_types.CHARACTER_GROUP_OVERLAY_STATE_CHANGE_AFTER   // 'character_group_overlay_state_change_after' - 角色群组复盖状态变更后
// 触发时机: 角色群组复盖层状态改变之后触发

// 生成事件
event_types.GENERATION_STARTED                  // 'generation_started' - 生成已开始
// 触发时机: AI开始生成回復时触发

event_types.GENERATION_STOPPED                  // 'generation_stopped' - 生成已停止
// 触发时机: AI生成被用户手动停止时触发

event_types.GENERATION_ENDED                    // 'generation_ended' - 生成已结束
// 触发时机: AI完成回復生成时触发

event_types.GENERATION_AFTER_COMMANDS           // 'GENERATION_AFTER_COMMANDS' - 命令执行后生成
// 触发时机: 斜杠命令执行完成后开始生成时触发

event_types.STREAM_TOKEN_RECEIVED               // 'stream_token_received' - 流式token已接收
// 触发时机: 在流式生成模式下，每收到一个token时触发

event_types.GENERATE_BEFORE_COMBINE_PROMPTS     // 'generate_before_combine_prompts' - 合併提示前生成
// 触发时机: 在合併所有提示组件之前触发

event_types.GENERATE_AFTER_COMBINE_PROMPTS      // 'generate_after_combine_prompts' - 合併提示后生成
// 触发时机: 在合併所有提示组件之后触发

event_types.GENERATE_AFTER_DATA                 // 'generate_after_data' - 数据处理后生成
// 触发时机: 生成数据处理完成后触发

// 群组事件
event_types.GROUP_UPDATED                       // 'group_updated' - 群组已更新
// 触发时机: 群组信息被修改时触发

event_types.GROUP_CHAT_CREATED                  // 'group_chat_created' - 群组聊天已创建
// 触发时机: 新的群组聊天被创建时触发

event_types.GROUP_CHAT_DELETED                  // 'group_chat_deleted' - 群组聊天已删除
// 触发时机: 群组聊天被删除时触发

event_types.GROUP_MEMBER_DRAFTED                // 'group_member_drafted' - 群组成员已起草
// 触发时机: 群组成员被选中准备发言时触发

// 扩展事件
event_types.EXTENSIONS_FIRST_LOAD               // 'extensions_first_load' - 扩展首次加载
// 触发时机: 所有扩展首次加载完成时触发，插件初始化的理想时机

event_types.EXTENSION_SETTINGS_LOADED           // 'extension_settings_loaded' - 扩展设置已加载
// 触发时机: 扩展设置从存储中加载完成时触发

event_types.EXTRAS_CONNECTED                    // 'extras_connected' - Extras已连接
// 触发时机: SillyTavern Extras服务连接成功时触发

// 世界信息事件
event_types.WORLDINFO_SETTINGS_UPDATED          // 'worldinfo_settings_updated' - 世界信息设置已更新
// 触发时机: 世界信息的设置被修改时触发

event_types.WORLDINFO_UPDATED                   // 'worldinfo_updated' - 世界信息已更新
// 触发时机: 世界信息条目被添加、修改或删除时触发

event_types.WORLDINFO_FORCE_ACTIVATE            // 'worldinfo_force_activate' - 强制激活世界信息
// 触发时机: 世界信息被强制激活时触发

event_types.WORLD_INFO_ACTIVATED                // 'world_info_activated' - 世界信息已激活
// 触发时机: 世界信息条目在生成过程中被激活时触发

// UI事件
event_types.USER_MESSAGE_RENDERED               // 'user_message_rendered' - 用户消息已渲染
// 触发时机: 用户消息在UI中渲染完成后触发

event_types.MOVABLE_PANELS_RESET                // 'movable_panels_reset' - 可移动面板已重置
// 触发时机: 可移动UI面板位置被重置时触发

event_types.FORCE_SET_BACKGROUND                // 'force_set_background' - 强制设置背景
// 触发时机: 背景被强制设置或更改时触发

event_types.IMAGE_SWIPED                        // 'image_swiped' - 图片已滑动
// 触发时机: 用户滑动查看不同的图片时触发

// API事件
event_types.CHATCOMPLETION_SOURCE_CHANGED       // 'chatcompletion_source_changed' - 聊天完成源已更改
// 触发时机: 聊天完成API源（如OpenAI、Claude等）被切换时触发

event_types.CHATCOMPLETION_MODEL_CHANGED        // 'chatcompletion_model_changed' - 聊天完成模型已更改
// 触发时机: 聊天完成模型被切换时触发

event_types.OAI_PRESET_CHANGED_BEFORE           // 'oai_preset_changed_before' - OpenAI预设更改前
// 触发时机: OpenAI预设即将被更改时触发

event_types.OAI_PRESET_CHANGED_AFTER            // 'oai_preset_changed_after' - OpenAI预设更改后
// 触发时机: OpenAI预设更改完成后触发

event_types.OAI_PRESET_EXPORT_READY             // 'oai_preset_export_ready' - OpenAI预设导出就绪
// 触发时机: OpenAI预设准备导出时触发

event_types.OAI_PRESET_IMPORT_READY             // 'oai_preset_import_ready' - OpenAI预设导入就绪
// 触发时机: OpenAI预设导入完成时触发

event_types.TEXT_COMPLETION_SETTINGS_READY      // 'text_completion_settings_ready' - 文本完成设置就绪
// 触发时机: 文本完成设置加载并准备就绪时触发

event_types.CHAT_COMPLETION_SETTINGS_READY      // 'chat_completion_settings_ready' - 聊天完成设置就绪
// 触发时机: 聊天完成设置加载并准备就绪时触发

event_types.CHAT_COMPLETION_PROMPT_READY        // 'chat_completion_prompt_ready' - 聊天完成提示就绪
// 触发时机: 聊天完成提示构建完成并准备发送时触发

// 其他事件
event_types.IMPERSONATE_READY                   // 'impersonate_ready' - 模拟准备就绪
// 触发时机: 用户模拟模式准备就绪时触发

event_types.FILE_ATTACHMENT_DELETED             // 'file_attachment_deleted' - 文件附件已删除
// 触发时机: 消息中的文件附件被删除时触发

event_types.ONLINE_STATUS_CHANGED               // 'online_status_changed' - 在线状态已更改
// 触发时机: 应用的在线/离线状态发生变化时触发

event_types.CONNECTION_PROFILE_LOADED           // 'connection_profile_loaded' - 连接配置已加载
// 触发时机: API连接配置文件加载完成时触发

event_types.TOOL_CALLS_PERFORMED                // 'tool_calls_performed' - 工具调用已执行
// 触发时机: AI工具调用执行完成时触发

event_types.TOOL_CALLS_RENDERED                 // 'tool_calls_rendered' - 工具调用已渲染
// 触发时机: 工具调用结果在UI中渲染完成时触发

event_types.OPEN_CHARACTER_LIBRARY              // 'open_character_library' - 打开角色库
// 触发时机: 角色库界面被打开时触发
```

### 事件监听和发送

```javascript
// 监听事件
eventSource.on(event_types.MESSAGE_RECEIVED, handleMessage);

function handleMessage(data) {
    console.log('收到消息:', data.message);
    // 处理消息逻辑
}

// 发送事件
await eventSource.emit(event_types.CUSTOM_EVENT, data);

// 移除事件监听器
eventSource.off(event_types.MESSAGE_RECEIVED, handleMessage);
```

---

## ⚙️ 设置管理

### 设置初始化

```javascript
const MODULE_NAME = 'my_extension';

const defaultSettings = {
    enabled: false,
    option1: 'default',
    option2: 5
};

function getSettings() {
    // 初始化设置
    if (!extension_settings[MODULE_NAME]) {
        extension_settings[MODULE_NAME] = structuredClone(defaultSettings);
    }
    
    // 确保所有默认键存在
    for (const key in defaultSettings) {
        if (extension_settings[MODULE_NAME][key] === undefined) {
            extension_settings[MODULE_NAME][key] = defaultSettings[key];
        }
    }
    
    return extension_settings[MODULE_NAME];
}
```

### 设置保存

```javascript
// 使用防抖保存
const { saveSettingsDebounced } = SillyTavern.getContext();

// 修改设置
const settings = getSettings();
settings.option1 = 'new value';

// 保存设置
saveSettingsDebounced();
```

### UI设置绑定

```javascript
function onSettingChange(event) {
    const value = $(event.target).prop('checked');
    extension_settings[MODULE_NAME].enabled = value;
    saveSettingsDebounced();
}

// 绑定事件
$("#my_setting").on("input", onSettingChange);

// 加载设置到UI
$("#my_setting").prop("checked", extension_settings[MODULE_NAME].enabled);
```

---

## 🎨 UI组件

### HTML模板加载

```javascript
// 加载HTML文件
const settingsHtml = await $.get(`${extensionFolderPath}/example.html`);

// 添加到设置面板
$("#extensions_settings").append(settingsHtml);  // 左栏
$("#extensions_settings2").append(settingsHtml); // 右栏
```

### 模板渲染

```javascript
// 异步渲染 (推荐)
const html = await renderExtensionTemplateAsync(extensionName, 'templateId', data);
```

### 常用UI元素

```html
<!-- 可折叠面板 -->
<div class="inline-drawer">
    <div class="inline-drawer-toggle inline-drawer-header">
        <b>面板标题</b>
        <div class="inline-drawer-icon fa-solid fa-circle-chevron-down down"></div>
    </div>
    <div class="inline-drawer-content">
        <!-- 面板内容 -->
    </div>
</div>

<!-- 按钮 -->
<input class="menu_button" type="submit" value="按钮文字" />

<!-- 複选框 -->
<input id="my_checkbox" type="checkbox" />
<label for="my_checkbox">选项标籤</label>

<!-- 分隔线 -->
<hr class="sysHR" />
```

### 弹窗和通知

```javascript
// Toast通知
toastr.info('信息内容', '标题');
toastr.success('成功消息');
toastr.warning('警告消息');
toastr.error('错误消息');

// 确认对话框
const result = await callGenericPopup('确认消息', POPUP_TYPE.CONFIRM);
if (result === POPUP_RESULT.AFFIRMATIVE) {
    // 用户确认
}
```

---

## ⌨️ 斜杠命令

### 註册斜杠命令

```javascript
import { SlashCommandParser } from '../../../slash-commands/SlashCommandParser.js';
import { SlashCommand } from '../../../slash-commands/SlashCommand.js';
import {
    ARGUMENT_TYPE,
    SlashCommandArgument,
    SlashCommandNamedArgument
} from '../../../slash-commands/SlashCommandArgument.js';

SlashCommandParser.addCommandObject(SlashCommand.fromProps({
    name: 'mycommand',
    callback: (namedArgs, unnamedArgs) => {
        // 命令逻辑
        return '返回结果';
    },
    aliases: ['alias1', 'alias2'],
    returns: '命令返回值描述',
    namedArgumentList: [
        SlashCommandNamedArgument.fromProps({
            name: 'times',
            description: '重複次数',
            typeList: ARGUMENT_TYPE.NUMBER,
            defaultValue: '5',
        }),
        SlashCommandNamedArgument.fromProps({
            name: 'enabled',
            description: '是否启用',
            typeList: ARGUMENT_TYPE.BOOLEAN,
            defaultValue: 'off',
            enumList: ['on', 'off'],
        }),
    ],
    unnamedArgumentList: [
        SlashCommandArgument.fromProps({
            description: '要处理的文本',
            typeList: ARGUMENT_TYPE.STRING,
            isRequired: true,
        }),
    ],
    helpString: `
        <div>命令说明</div>
        <div>
            <strong>示例:</strong>
            <pre><code>/mycommand times=3 hello world</code></pre>
        </div>
    `,
}));
```

### 参数类型

```javascript
ARGUMENT_TYPE.STRING         // 字符串类型
ARGUMENT_TYPE.NUMBER         // 数字类型
ARGUMENT_TYPE.BOOLEAN        // 布尔类型
ARGUMENT_TYPE.LIST           // 列表类型
ARGUMENT_TYPE.CLOSURE        // 闭包类型
ARGUMENT_TYPE.SUBCOMMAND     // 子命令类型
ARGUMENT_TYPE.DICTIONARY     // 字典类型
ARGUMENT_TYPE.VARIABLE_NAME  // 变量名类型
```

---

## 🔄 拦截器

### 生成拦截器

在manifest.json中定义:

```json
{
    "generate_interceptor": "myInterceptorFunction"
}
```

实现拦截器:

```javascript
globalThis.myInterceptorFunction = async function(chat, contextSize, abort, type) {
    // 修改聊天数据
    const systemNote = {
        is_user: false,
        name: "System Note",
        send_date: Date.now(),
        mes: "这是插件添加的消息!"
    };

    // 在最后一条消息前插入
    chat.splice(chat.length - 1, 0, systemNote);

    // 如果需要中止生成
    // abort(true); // true表示阻止后续拦截器运行
};
```

### 拦截器参数

* ​`chat`​: 聊天消息数组，可直接修改
* ​`contextSize`​: 当前上下文大小（token数）
* ​`abort`​: 中止函数，调用后停止生成
* ​`type`​: 生成类型 ('quiet', 'regenerate', 'impersonate', 'swipe' 等)

---

## 🚀 高级功能

### 工具调用 (Tool Calling)

```javascript
import { ToolManager } from '../../../tool-calling.js';

// 註册工具
ToolManager.registerTool({
    name: 'my_tool',
    description: '工具描述',
    parameters: {
        type: 'object',
        properties: {
            input: {
                type: 'string',
                description: '输入参数'
            }
        },
        required: ['input']
    },
    handler: async (parameters) => {
        // 工具逻辑
        return { result: '处理结果' };
    }
});

// 初始化工具斜杠命令
ToolManager.initToolSlashCommands();

// 获取可用工具
const tools = ToolManager.getAvailableTools();

// 调用工具
const result = await ToolManager.invokeTool('tool_name', parameters);
```

### 拖拽处理

```javascript
import { DragAndDropHandler } from '../../../dragdrop.js';

const dragHandler = new DragAndDropHandler();

// 监听拖拽事件
dragHandler.on('file-dropped', (files) => {
    files.forEach(file => {
        console.log('拖拽文件:', file.name);
    });
});

dragHandler.on('text-dropped', (text) => {
    console.log('拖拽文本:', text);
});
```

### 批量编辑复盖层

```javascript
import { BulkEditOverlay, CharacterContextMenu } from '../../../BulkEditOverlay.js';

const overlay = new BulkEditOverlay();
const contextMenu = new CharacterContextMenu(overlay);

// 显示批量编辑
overlay.show();

// 隐藏批量编辑
overlay.hide();

// 选择所有角色
overlay.selectAll();

// 取消选择所有角色
overlay.deselectAll();
```

### 过滤器助手

```javascript
import { FilterHelper, FILTER_STATES, FILTER_TYPES } from '../../../filters.js';

const filter = new FilterHelper(callback);

// 设置过滤数据
filter.setFilterData(FILTER_TYPES.CHARACTER, {
    name: 'character_name',
    state: FILTER_STATES.SELECTED
});

// 应用过滤器
filter.applyFilters();

// 清除过滤器
filter.clearFilters();

// 检查过滤器状态
const isFiltered = filter.isFiltered();
```

### 秒錶工具

```javascript
import { Stopwatch } from '../../../utils.js';

const stopwatch = new Stopwatch();

// 开始计时
stopwatch.start();

// 停止计时
stopwatch.stop();

// 获取经过时间
const elapsed = stopwatch.getElapsed(); // 毫秒

// 重置秒錶
stopwatch.reset();

// 检查是否正在运行
const isRunning = stopwatch.isRunning();
```

---

## 📖 完整API参考

### SillyTavern.getContext() 完整返回对象

```javascript
const context = SillyTavern.getContext();

// 聊天相关
context.chat                    // 当前聊天消息数组 - 可变
context.chatId                  // 当前聊天ID
context.chatMetadata           // 聊天元数据
context.saveChat()             // 保存聊天函数
context.clearChat()            // 清空聊天函数

// 角色相关
context.characters             // 角色列表
context.characterId            // 当前角色ID
context.saveCharacter()        // 保存角色函数
context.getCharacter(id)       // 获取指定角色

// 群组相关
context.groups                 // 群组列表
context.selectedGroup          // 当前选中群组
context.saveGroup()            // 保存群组函数

// 设置相关
context.extensionSettings      // 扩展设置对象
context.saveSettings()         // 保存设置函数
context.saveSettingsDebounced  // 防抖保存设置函数
context.saveMetadata()         // 保存元数据函数

// 工具函数
context.addLocaleData(locale, data)  // 添加本地化数据
context.renderTemplate(path, data)   // 渲染模板
context.getTokenCount(text)          // 获取token数量
context.generateQuietPrompt(text)    // 静默生成提示
```

### 完整工具函数参考

#### utils.js 工具函数

```javascript
import {
    // 时间和延迟
    debounce,                    // 防抖函数
    delay,                       // 延迟函数
    waitUntilCondition,          // 等待条件满足
    Stopwatch,                   // 秒錶类

    // 字符串处理
    trimToEndSentence,           // 修剪到句子结尾
    escapeRegex,                 // 转义正则表达式
    getCharaFilename,            // 获取角色文件名

    // 数组和对象
    countOccurrences,            // 计算出现次数
    isOdd,                       // 检查是否为奇数
    onlyUnique,                  // 数组去重
    sortMoments,                 // 排序时刻

    // 文件和数据
    download,                    // 下载函数
    isDataURL,                   // 检查是否为数据URL
    getBase64Async,              // 异步获取Base64
    humanFileSize,               // 人性化文件大小
    ensureImageFormatSupported,  // 确保图片格式支持

    // URL和验证
    isValidUrl,                  // 验证URL
    timestampToMoment,           // 时间戳转时刻

    // UI和DOM
    flashHighlight,              // 闪烁高亮
    toggleDrawer,                // 切换抽屉
    resetScrollHeight,           // 重置滚动高度
    isElementInViewport,         // 检查元素是否在视口
    copyText,                    // 複製文本

    // 逻辑判断
    isTrueBoolean,               // 检查真值布尔

    // 模板和常量
    PAGINATION_TEMPLATE,         // 分页模板
} from './scripts/utils.js';
```

#### script.js 主要导出函数

```javascript
import {
    // 用户和头像
    user_avatar,                 // 用户头像
    setUserAvatar,               // 设置用户头像
    getUserAvatars,              // 获取用户头像列表
    getUserAvatar,               // 获取用户头像

    // 聊天和角色
    characters,                  // 角色列表
    this_chid,                   // 当前角色ID
    chat,                        // 聊天记录
    chat_metadata,               // 聊天元数据

    // 系统常量
    systemUserName,              // 系统用户名
    neutralCharacterName,        // 中性角色名
    name1,                       // 用户名
    name2,                       // 角色名
    default_avatar,              // 默认头像
    system_avatar,               // 系统头像
    comment_avatar,              // 评论头像
    default_user_avatar,         // 默认用户头像
    CLIENT_VERSION,              // 客户端版本

    // 状态和设置
    isChatSaving,                // 是否正在保存聊天
    online_status,               // 在线状态
    api_server,                  // API服务器
    is_send_press,               // 是否正在发送
    settings,                    // 设置
    amount_gen,                  // 生成数量
    max_context,                 // 最大上下文
    extension_prompts,           // 扩展提示
    main_api,                    // 主API
    active_character,            // 活动角色
    active_group,                // 活动群组

    // 动画和UI
    ANIMATION_DURATION_DEFAULT,  // 默认动画持续时间
    animation_duration,          // 动画持续时间
    animation_easing,            // 动画缓动

    // 防抖函数
    saveSettingsDebounced,       // 防抖保存设置
    saveCharacterDebounced,      // 防抖保存角色
    printCharactersDebounced,    // 防抖打印角色

    // 超时常量
    DEFAULT_SAVE_EDIT_TIMEOUT,   // 默认保存编辑超时
    DEFAULT_PRINT_TIMEOUT,       // 默认打印超时

    // 枚举
    system_message_types,        // 系统消息类型
    extension_prompt_types,      // 扩展提示类型
    extension_prompt_roles,      // 扩展提示角色

    // 常量
    MAX_INJECTION_DEPTH,         // 最大注入深度
    talkativeness_default,       // 默认健谈度
    depth_prompt_depth_default,  // 默认深度提示深度
    depth_prompt_role_default,   // 默认深度提示角色

    // 过滤器
    entitiesFilter,              // 实体过滤器

    // 流处理
    streamingProcessor,          // 流处理器
    abortStatusCheck,            // 中止状态检查

    // 提示相关
    itemizedPrompts,             // 项目化提示
    mesForShowdownParse,         // Showdown解析消息

    // 系统消息
    system_messages,             // 系统消息

    // 连接和API管理
    CONNECT_API_MAP,             // 连接API映射
    UNIQUE_APIS,                 // 唯一API列表
    getRequestHeaders,           // 获取请求头
    pingServer,                  // Ping服务器
    getCurrentChatId,            // 获取当前聊天ID

    // 模板和渲染
    reloadMarkdownProcessor,     // 重新加载Markdown处理器

    // 状态管理
    firstRun,                    // 首次运行
    settingsReady,               // 设置就绪
    currentVersion,              // 当前版本
    displayVersion,              // 显示版本

    // 聊天相关
    generatedPromptCache,        // 生成的提示缓存
    generation_started,          // 生成开始时间
    saveCharactersPage,          // 保存角色页面

    // UI相关
    optionsPopper,               // 选项弹出器
    exportPopper,                // 导出弹出器
    isExportPopupOpen,           // 导出弹窗是否打开
    messageTemplate,             // 消息模板
    chatElement,                 // 聊天元素

    // 对话框和弹窗
    dialogueResolve,             // 对话框解析
    dialogueCloseStop,           // 对话框关闭停止

    // 其他状态
    crop_data,                   // 裁剪数据
    is_delete_mode,              // 是否删除模式
    fav_ch_checked,              // 收藏角色已检查
    scrollLock,                  // 滚动锁定
    charDragDropHandler,         // 角色拖拽处理器

    // 编辑相关
    this_del_mes,                // 要删除的消息
    this_edit_mes_chname,        // 编辑消息角色名
    this_edit_mes_id,            // 编辑消息ID

    // 设置相关
    preset_settings,             // 预设设置
    swipes,                      // 滑动

    // 提示相关
    PromptArrayItemForRawPromptDisplay,      // 原始提示显示的提示数组项
    priorPromptArrayItemForRawPromptDisplay, // 先前的原始提示显示项

    // 安全聊天
    SAFETY_CHAT,                 // 安全聊天

    // 其他
    popup_type,                  // 弹窗类型
    chat_file_for_del,           // 要删除的聊天文件
    is_advanced_char_open,       // 高级角色是否打开

    // 中止控制器
    abortController,             // 中止控制器
} from '../../../../script.js';
```

#### extensions.js 扩展函数

```javascript
import {
    // 核心函数
    getContext,                  // 获取上下文
    getApiUrl,                   // 获取API URL

    // 扩展管理
    extensionNames,              // 扩展名称列表
    extensionTypes,              // 扩展类型映射
    modules,                     // 活动模块列表
    extension_settings,          // 扩展设置

    // 扩展操作
    enableExtension,             // 启用扩展
    disableExtension,            // 禁用扩展
    installExtension,            // 安装扩展
    deleteExtension,             // 删除扩展

    // 设置管理
    loadExtensionSettings,       // 加载扩展设置
    saveMetadataDebounced,       // 防抖保存元数据

    // 模板渲染
    renderExtensionTemplate,     // 渲染扩展模板 (已弃用)
    renderExtensionTemplateAsync, // 异步渲染扩展模板

    // 拦截器
    runGenerationInterceptors,   // 运行生成拦截器

    // 字符数据
    writeExtensionField,         // 写入扩展字段

    // 第三方扩展
    openThirdPartyExtensionMenu, // 打开第三方扩展菜单

    // 初始化
    initExtensions,              // 初始化扩展
    doDailyExtensionUpdatesCheck, // 执行每日扩展更新检查

    // 工具类
    ModuleWorkerWrapper,         // 模块工作器包装器

    // Extras API
    doExtrasFetch,               // 执行Extras获取

    // 连接管理
    connectToApi,                // 连接到API
    updateStatus,                // 更新状态
    addExtensionScript,          // 添加扩展脚本
    addExtensionStyle,           // 添加扩展样式
    activateExtensions,          // 激活扩展
    sortManifestsByOrder,        // 按顺序排序清单

    // 扩展状态
    activeExtensions,            // 活动扩展
    manifests,                   // 清单

    // 工具函数
    isSubsetOf,                  // 是否为子集
} from '../../../extensions.js';
```

#### 其他重要模块导出

```javascript
// power-user.js
import {
    collapseNewlines,            // 折叠换行
    loadPowerUserSettings,       // 加载高级用户设置
    playMessageSound,            // 播放消息声音
    fixMarkdown,                 // 修復Markdown
    power_user,                  // 高级用户设置对象
    persona_description_positions, // 人格描述位置
    loadMovingUIState,           // 加载移动UI状态
    getCustomStoppingStrings,    // 获取自定义停止字符串
    MAX_CONTEXT_DEFAULT,         // 默认最大上下文
    MAX_RESPONSE_DEFAULT,        // 默认最大响应
    renderStoryString,           // 渲染故事字符串
    sortEntitiesList,            // 排序实体列表
    registerDebugFunction,       // 註册调试函数
    flushEphemeralStoppingStrings, // 刷新临时停止字符串
    context_presets,             // 上下文预设
    resetMovableStyles,          // 重置可移动样式
    forceCharacterEditorTokenize, // 强制角色编辑器标记化
    applyPowerUserSettings,      // 应用高级用户设置
} from '../../../power-user.js';

// world-info.js
import {
    world_info,                  // 世界信息数组
    getWorldInfoPrompt,          // 获取世界信息提示
    getWorldInfoSettings,        // 获取世界信息设置
    setWorldInfoSettings,        // 设置世界信息设置
    world_names,                 // 世界名称
    importEmbeddedWorldInfo,     // 导入嵌入式世界信息
    checkEmbeddedWorld,          // 检查嵌入式世界
    setWorldInfoButtonClass,     // 设置世界信息按钮类
    importWorldInfo,             // 导入世界信息
    wi_anchor_position,          // 世界信息锚点位置
    world_info_include_names,    // 世界信息包含名称
} from '../../../world-info.js';

// openai.js
import {
    setOpenAIMessageExamples,    // 设置OpenAI消息示例
    setOpenAIMessages,           // 设置OpenAI消息
    setupChatCompletionPromptManager, // 设置聊天完成提示管理器
    prepareOpenAIMessages,       // 准备OpenAI消息
    sendOpenAIRequest,           // 发送OpenAI请求
    loadOpenAISettings,          // 加载OpenAI设置
    oai_settings,                // OpenAI设置
    openai_messages_count,       // OpenAI消息计数
    chat_completion_sources,     // 聊天完成源
    getChatCompletionModel,      // 获取聊天完成模型
    proxies,                     // 代理列表
    loadProxyPresets,            // 加载代理预设
    selected_proxy,              // 选中的代理
    initOpenAI,                  // 初始化OpenAI
} from '../../../openai.js';

// tags.js
import {
    tag_map,                     // 标籤映射
    tags,                        // 标籤数组
    filterByTagState,            // 按标籤状态过滤
    isBogusFolder,               // 是否为虚假文件夹
    isBogusFolderOpen,           // 虚假文件夹是否打开
    chooseBogusFolder,           // 选择虚假文件夹
    getTagBlock,                 // 获取标籤块
    loadTagsSettings,            // 加载标籤设置
    printTagFilters,             // 打印标籤过滤器
    getTagKeyForEntity,          // 获取实体标籤键
    printTagList,                // 打印标籤列表
    createTagMapFromList,        // 从列表创建标籤映射
    renameTagKey,                // 重命名标籤键
    importTags,                  // 导入标籤
    tag_filter_type,             // 标籤过滤器类型
    compareTagsForSort,          // 比较标籤排序
    initTags,                    // 初始化标籤
    applyTagsOnCharacterSelect,  // 在角色选择时应用标籤
    applyTagsOnGroupSelect,      // 在群组选择时应用标籤
    tag_import_setting,          // 标籤导入设置
} from '../../../tags.js';

// slash-commands.js
import {
    COMMENT_NAME_DEFAULT,        // 默认评论名称
    executeSlashCommandsOnChatInput, // 在聊天输入上执行斜杠命令
    getSlashCommandsHelp,        // 获取斜杠命令帮助
    initDefaultSlashCommands,    // 初始化默认斜杠命令
    isExecutingCommandsFromChatInput, // 是否正在从聊天输入执行命令
    pauseScriptExecution,        // 暂停脚本执行
    processChatSlashCommands,    // 处理聊天斜杠命令
    stopScriptExecution,         // 停止脚本执行
} from '../../../slash-commands.js';

// personas.js
import {
    user_avatar,                 // 用户头像
    getUserAvatars,              // 获取用户头像
    getUserAvatar,               // 获取用户头像
    setUserAvatar,               // 设置用户头像
    initPersonas,                // 初始化人格
    setPersonaDescription,       // 设置人格描述
    initUserAvatar,              // 初始化用户头像
} from '../../../personas.js';

// backgrounds.js
import {
    getBackgrounds,              // 获取背景
    initBackgrounds,             // 初始化背景
    loadBackgroundSettings,      // 加载背景设置
    background_settings,         // 背景设置
} from '../../../backgrounds.js';

// popup.js
import {
    POPUP_RESULT,                // 弹窗结果枚举
    POPUP_TYPE,                  // 弹窗类型枚举
    Popup,                       // 弹窗类
    callGenericPopup,            // 调用通用弹窗
    fixToastrForDialogs,         // 修復对话框的Toastr
} from '../../../popup.js';

// templates.js
import {
    renderTemplate,              // 渲染模板
    renderTemplateAsync,         // 异步渲染模板
} from '../../../templates.js';

// user.js
import {
    currentUser,                 // 当前用户
    setUserControls,             // 设置用户控制
    isAdmin,                     // 是否为管理员
} from '../../../user.js';

// secrets.js
import {
    SECRET_KEYS,                 // 密钥键
    readSecretState,             // 读取密钥状态
    secret_state,                // 密钥状态
    writeSecret,                 // 写入密钥
} from '../../../secrets.js';

// i18n.js
import {
    initLocales,                 // 初始化语言环境
    t,                           // 翻译函数
} from '../../../i18n.js';

// tokenizers.js
import {
    getFriendlyTokenizerName,    // 获取友好的标记器名称
    getTokenCount,               // 获取标记数量
    getTokenCountAsync,          // 异步获取标记数量
    initTokenizers,              // 初始化标记器
    saveTokenCache,              // 保存标记缓存
    TOKENIZER_SUPPORTED_KEY,     // 标记器支持键
} from '../../../tokenizers.js';

// macros.js
import {
    evaluateMacros,              // 评估宏
    getLastMessageId,            // 获取最后消息ID
    initMacros,                  // 初始化宏
} from '../../../macros.js';

// chats.js
import {
    appendFileContent,           // 追加文件内容
    hasPendingFileAttachment,    // 是否有待处理文件附件
    populateFileAttachment,      // 填充文件附件
    decodeStyleTags,             // 解码样式标籤
    encodeStyleTags,             // 编码样式标籤
    isExternalMediaAllowed,      // 是否允许外部媒体
    getCurrentEntityId,          // 获取当前实体ID
    preserveNeutralChat,         // 保留中性聊天
    restoreNeutralChat,          // 恢復中性聊天
} from '../../../chats.js';
```

### 系统常量和枚举

#### 系统消息类型

```javascript
system_message_types.HELP           // 'help' - 帮助消息
system_message_types.WELCOME        // 'welcome' - 欢迎消息
system_message_types.GROUP          // 'group' - 群组消息
system_message_types.EMPTY          // 'empty' - 空消息
system_message_types.GENERIC        // 'generic' - 通用消息
system_message_types.NARRATOR       // 'narrator' - 旁白消息
system_message_types.COMMENT        // 'comment' - 评论消息
system_message_types.SLASH_COMMANDS // 'slash_commands' - 斜杠命令消息
system_message_types.FORMATTING     // 'formatting' - 格式化消息
system_message_types.HOTKEYS        // 'hotkeys' - 热键消息
system_message_types.MACROS         // 'macros' - 宏消息
system_message_types.WELCOME_PROMPT // 'welcome_prompt' - 欢迎提示
system_message_types.ASSISTANT_NOTE // 'assistant_note' - 助手註释
```

#### 扩展提示类型

```javascript
extension_prompt_types.NONE          // -1 - 无
extension_prompt_types.IN_PROMPT     // 0 - 在提示中
extension_prompt_types.IN_CHAT       // 1 - 在聊天中
extension_prompt_types.BEFORE_PROMPT // 2 - 在提示前
```

#### 扩展提示角色

```javascript
extension_prompt_roles.SYSTEM        // 0 - 系统角色
extension_prompt_roles.USER          // 1 - 用户角色
extension_prompt_roles.ASSISTANT     // 2 - 助手角色
```

#### 弹窗类型和结果

```javascript
// 弹窗类型
POPUP_TYPE.TEXT                      // 文本弹窗
POPUP_TYPE.CONFIRM                   // 确认弹窗
POPUP_TYPE.INPUT                     // 输入弹窗
POPUP_TYPE.DISPLAY                   // 显示弹窗

// 弹窗结果
POPUP_RESULT.AFFIRMATIVE             // 确认结果
POPUP_RESULT.NEGATIVE                // 否定结果
POPUP_RESULT.CANCELLED               // 取消结果
POPUP_RESULT.INTERRUPTED             // 中断结果
```

#### 过滤器状态和类型

```javascript
// 过滤器状态
FILTER_STATES.SELECTED               // 选中状态
FILTER_STATES.EXCLUDED               // 排除状态
FILTER_STATES.UNDEFINED              // 未定义状态

// 过滤器类型
FILTER_TYPES.CHARACTER               // 角色过滤器
FILTER_TYPES.GROUP                   // 群组过滤器
FILTER_TYPES.TAG                     // 标籤过滤器
FILTER_TYPES.FOLDER                  // 文件夹过滤器
```

#### 聊天完成源

```javascript
chat_completion_sources.OPENAI               // OpenAI
chat_completion_sources.WINDOWAI             // WindowAI
chat_completion_sources.CLAUDE               // Claude
chat_completion_sources.SCALE                // Scale
chat_completion_sources.OPENROUTER           // OpenRouter
chat_completion_sources.AI21                 // AI21
chat_completion_sources.MAKERSUITE           // MakerSuite
chat_completion_sources.MISTRALAI            // MistralAI
chat_completion_sources.CUSTOM               // 自定义
```

#### 防抖超时常量

```javascript
debounce_timeout.quick               // 100ms - 快速
debounce_timeout.short               // 300ms - 短
debounce_timeout.relaxed             // 1000ms - 放松
debounce_timeout.extended            // 3000ms - 扩展
```

#### 默认值常量

```javascript
MAX_CONTEXT_DEFAULT                  // 默认最大上下文
MAX_RESPONSE_DEFAULT                 // 默认最大响应
MAX_INJECTION_DEPTH                  // 最大注入深度 (1000)
ANIMATION_DURATION_DEFAULT           // 默认动画持续时间 (125ms)
talkativeness_default                // 默认健谈度 (0.5)
depth_prompt_depth_default           // 默认深度提示深度 (4)
depth_prompt_role_default            // 默认深度提示角色 ('system')
```

#### 头像常量

```javascript
default_avatar                       // 'img/ai4.png' - 默认头像
system_avatar                        // 'img/five.png' - 系统头像
comment_avatar                       // 'img/quill.png' - 评论头像
default_user_avatar                  // 'img/user-default.png' - 默认用户头像
```

#### 系统用户名

```javascript
systemUserName                       // 'SillyTavern System' - 系统用户名
neutralCharacterName                 // 'Assistant' - 中性角色名
COMMENT_NAME_DEFAULT                 // 默认评论名称
```

## 💡 实用案例

### 案例1: 消息统计插件

```javascript
// 统计消息数量和字符数
const extensionName = "message-stats";
let messageStats = {
    totalMessages: 0,
    totalCharacters: 0,
    userMessages: 0,
    aiMessages: 0
};

function updateStats(data) {
    messageStats.totalMessages++;
    messageStats.totalCharacters += data.message.length;

    if (data.is_user) {
        messageStats.userMessages++;
    } else {
        messageStats.aiMessages++;
    }

    updateStatsDisplay();
}

function updateStatsDisplay() {
    $('#stats-total').text(messageStats.totalMessages);
    $('#stats-chars').text(messageStats.totalCharacters);
    $('#stats-user').text(messageStats.userMessages);
    $('#stats-ai').text(messageStats.aiMessages);
}

// 监听消息事件
eventSource.on(event_types.MESSAGE_SENT, updateStats);
eventSource.on(event_types.MESSAGE_RECEIVED, updateStats);
```

### 案例2: 自动保存插件

```javascript
// 定期自动保存聊天
const extensionName = "auto-save";
let autoSaveInterval;

const defaultSettings = {
    enabled: true,
    interval: 300000, // 5分钟
    showNotification: true
};

function startAutoSave() {
    const settings = getSettings();
    if (!settings.enabled) return;

    autoSaveInterval = setInterval(async () => {
        try {
            const context = getContext();
            await context.saveChat();

            if (settings.showNotification) {
                toastr.success('聊天已自动保存', '', { timeOut: 2000 });
            }
        } catch (error) {
            console.error('自动保存失败:', error);
        }
    }, settings.interval);
}

function stopAutoSave() {
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
        autoSaveInterval = null;
    }
}

// 设置变更时重启自动保存
function onSettingsChange() {
    stopAutoSave();
    startAutoSave();
}
```

### 案例3: 快速回復插件

```javascript
// 添加快速回復按钮
const extensionName = "quick-replies";

const defaultReplies = [
    "继续",
    "详细说明",
    "换个话题",
    "我明白了",
    "有趣！"
];

function createQuickReplyButtons() {
    const container = $('<div class="quick-replies-container"></div>');

    defaultReplies.forEach(reply => {
        const button = $(`<button class="quick-reply-btn">${reply}</button>`);
        button.on('click', () => sendQuickReply(reply));
        container.append(button);
    });

    return container;
}

function sendQuickReply(text) {
    const textarea = $('#send_textarea');
    textarea.val(text);
    $('#send_but').trigger('click');
}

// 在聊天区域添加快速回復按钮
function addQuickReplies() {
    const quickReplies = createQuickReplyButtons();
    $('#send_form').after(quickReplies);
}

jQuery(() => {
    addQuickReplies();
});
```

### 案例4: 角色情绪分析插件

```javascript
// 分析角色消息的情绪
const extensionName = "emotion-analyzer";

const emotions = {
    happy: ['😊', '😄', '😃', '🙂'],
    sad: ['😢', '😭', '😞', '☹️'],
    angry: ['😠', '😡', '🤬', '😤'],
    surprised: ['😲', '😮', '🤯', '😯'],
    neutral: ['😐', '😑', '🙄', '😶']
};

function analyzeEmotion(message) {
    const text = message.toLowerCase();

    // 简单的关键词匹配
    const happyWords = ['happy', 'joy', 'glad', 'excited', '开心', '高兴'];
    const sadWords = ['sad', 'cry', 'upset', 'depressed', '伤心', '难过'];
    const angryWords = ['angry', 'mad', 'furious', 'annoyed', '生气', '愤怒'];

    if (happyWords.some(word => text.includes(word))) {
        return 'happy';
    } else if (sadWords.some(word => text.includes(word))) {
        return 'sad';
    } else if (angryWords.some(word => text.includes(word))) {
        return 'angry';
    }

    return 'neutral';
}

function addEmotionIndicator(messageElement, emotion) {
    const emoji = emotions[emotion][0];
    const indicator = $(`<span class="emotion-indicator" title="检测到情绪: ${emotion}">${emoji}</span>`);
    messageElement.find('.mes_text').prepend(indicator);
}

// 监听消息渲染事件
eventSource.on(event_types.CHARACTER_MESSAGE_RENDERED, (data) => {
    const emotion = analyzeEmotion(data.message);
    addEmotionIndicator(data.element, emotion);
});
```

---

## 📋 最佳实践

### 1. 命名规范

```javascript
// 使用一致的命名
const extensionName = "my-extension";
const MODULE_NAME = 'my_extension';
const CSS_CLASS_PREFIX = 'my-extension-';
```

### 2. 错误处理

```javascript
try {
    // 可能出错的代码
    await riskyOperation();
} catch (error) {
    console.error('插件错误:', error);
    toastr.error('操作失败，请查看控制台');
}
```

### 3. 性能优化

```javascript
// 使用防抖
const debouncedFunction = debounce(() => {
    // 执行逻辑
}, 300);

// 避免频繁DOM操作
const $element = $('#my-element'); // 缓存jQuery对象
```

### 4. 国际化支持

```javascript
// 添加翻译数据
SillyTavern.getContext().addLocaleData('zh-cn', {
    'Hello': '你好',
    'Settings': '设置'
});

// 使用翻译
const text = t`Hello`; // 自动翻译
```

### 5. 清理资源

```javascript
// 在适当时机清理事件监听器
function cleanup() {
    eventSource.off(event_types.MESSAGE_RECEIVED, handleMessage);
    $('#my-button').off('click', onButtonClick);
}
```

### 6. 版本兼容性

```javascript
// 检查API可用性
if (typeof SillyTavern !== 'undefined' && SillyTavern.getContext) {
    const context = SillyTavern.getContext();
    // 使用新API
} else {
    // 降级处理
}
```

### 7. 调试技巧

```javascript
// 使用有意义的日誌
console.debug(`[${extensionName}] 初始化完成`);
console.warn(`[${extensionName}] 警告信息`);
console.error(`[${extensionName}] 错误信息`);

// 註册调试函数
registerDebugFunction('myExtensionDebug', () => {
    return {
        settings: extension_settings[MODULE_NAME],
        status: 'active'
    };
});
```

### 8. 完整插件模板

```javascript
import { extension_settings, getContext } from "../../../extensions.js";
import { saveSettingsDebounced, eventSource, event_types } from "../../../../script.js";

const extensionName = "example-extension";
const extensionFolderPath = `scripts/extensions/third-party/${extensionName}`;

const defaultSettings = {
    enabled: true,
    message: "Hello from extension!"
};

function getSettings() {
    if (!extension_settings[extensionName]) {
        extension_settings[extensionName] = structuredClone(defaultSettings);
    }
    return extension_settings[extensionName];
}

function onToggleEnabled(event) {
    const settings = getSettings();
    settings.enabled = $(event.target).prop('checked');
    saveSettingsDebounced();
}

function onMessageReceived(data) {
    const settings = getSettings();
    if (settings.enabled) {
        console.log('收到消息:', data.message);
    }
}

jQuery(async () => {
    // 加载UI
    const html = await $.get(`${extensionFolderPath}/ui.html`);
    $("#extensions_settings").append(html);

    // 绑定事件
    $("#example_enabled").on("input", onToggleEnabled);
    eventSource.on(event_types.MESSAGE_RECEIVED, onMessageReceived);

    // 初始化设置
    const settings = getSettings();
    $("#example_enabled").prop("checked", settings.enabled);

    console.log(`[${extensionName}] 插件已加载`);
});
```
